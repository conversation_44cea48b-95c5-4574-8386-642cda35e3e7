{"name": "smarties-web", "version": "0.1.0", "private": true, "scripts": {"dev": "run-p dev:*", "dev:next": "next dev", "build": "next build", "build:test": "cross-env ANALYZE=true NODE_ENV=test next build", "start": "next start", "prepare": "husky", "lint-staged": "lint-staged", "lint": "next lint"}, "husky": {"hooks": {"pre-commit": "npx lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --ext .js,.jsx,.ts,.tsx --fix", "prettier --write"], "src/**/*.{less,css}": ["prettier --write"]}, "dependencies": {"@aws-sdk/client-lambda": "^3.631.0", "@heroicons/react": "2.1.5", "@hookform/resolvers": "^3.7.0", "@paddle/paddle-js": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/themes": "^3.1.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-table": "^8.19.2", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "docx": "^9.4.1", "dom-to-image": "^2.6.0", "embla-carousel-react": "^8.1.6", "encoding": "^0.1.13", "file-saver": "^2.0.5", "html-react-parser": "^5.2.2", "html-to-docx": "^1.8.0", "html-to-image": "^1.11.13", "i18next": "23.11.5", "immer": "^10.1.1", "lodash": "4.17.21", "lucide-react": "^0.400.0", "marked": "^14.0.0", "mermaid": "^11.6.0", "mitt": "^3.0.1", "mixpanel-browser": "^2.61.0", "next": "14.2.4", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "nookies": "^2.5.2", "pdfjs-dist": "^3.11.174", "react": "18", "react-copy-to-clipboard": "5.1.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-hook-form": "^7.52.1", "react-i18next": "^14.1.2", "react-pdf": "8.0.2", "react-resizable-panels": "2.1.7", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.1", "zod": "^3.23.8", "zustand": "5.0.0-rc.2"}, "devDependencies": {"@types/dom-to-image": "^2.6.7", "@types/file-saver": "^2.0.7", "@types/html-to-docx": "^1.8.0", "@types/lodash": "^4.17.7", "@types/mixpanel-browser": "^2.51.0", "@types/node": "^20", "@types/postcss-pxtorem": "^6.0.3", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@types/react-pdf": "^7.0.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "autoprefixer": "^10.4.20", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "husky": "^9.0.11", "lint-staged": "^15.2.7", "npm-run-all": "^4.1.5", "postcss": "^8", "postcss-pxtorem": "^6.1.0", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "sass": "^1.77.6", "tailwindcss": "^3.4.1", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}