# 前端开发规范文档

## 概述

本文档基于 Airbnb JavaScript 规范，结合 React 最佳实践，制定了完整的前端开发规范。规范等级分为：
- **Mandatory (必须)**: 基于 RFC 2119 的 MUST, MUST NOT, REQUIRED
- **Preferable (推荐)**: 基于 RFC 2119 的 SHOULD, SHOULD NOT  
- **Optional (可选)**: 基于 RFC 2119 的 MAY

## ESLint 配置

### 基础配置

```bash
# 安装 ESLint 相关依赖
npm install eslint @types/eslint eslint-plugin-prettier --save-dev
npm install eslint-plugin-react
npm install @typescript-eslint/eslint-plugin @typescript-eslint/parser --save-dev
npm install prettier eslint-plugin-prettier --save-dev
```

### .eslintrc.js 配置文件

```javascript
module.exports = {
  extends: [
    'airbnb-base',
    'prettier',
    'plugin:react/recommended',
    'plugin:import/typescript',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react-hooks'],
  env: {
    browser: true,
    node: true,
    jest: true,
  },
  settings: {
    react: {
      pragma: 'React',
      version: 'detect',
    },
  },
  parserOptions: {
    ecmaVersion: 2019,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    'no-use-before-define': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/indent': ['off', 2],
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/camelcase': 'off',
    '@typescript-eslint/no-empty-interface': 'error',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-use-before-define': ['error', { functions: false }],
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'import/order': 'error',
    'import/extensions': 'off',
    'import/no-named-as-default': 'off',
    'import/prefer-default-export': 'off',
    'import/no-extraneous-dependencies': 'off',
    'import/no-cycle': 'off',
    'import/no-unresolved': 'off',
    'react/display-name': 'off',
    'react-hooks/exhaustive-deps': 'warn',
    'react-hooks/rules-of-hooks': 'error',
    'react/prop-types': 'off',
    'max-len': 'off',
    'no-shadow': 'off',
    'no-console': 'off',
    'no-throw-literal': 'off',
    'no-unused-expressions': 'off',
    'no-bitwise': 'off',
    'no-useless-return': 'off',
    'no-plusplus': [
      'error',
      {
        allowForLoopAfterthoughts: true,
      },
    ],
    'no-continue': 'off',
    'no-return-assign': 'off',
    'no-restricted-syntax': 'off',
    'no-restricted-globals': 'off',
    'no-unneeded-ternary': 'off',
    'eol-last': 'error',
    'func-names': 'off',
    'consistent-return': 'off',
    'default-case': 'off',
    'react/react-in-jsx-scope': 'off',
    'react/jsx-uses-react': 'off',
    'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx', '.ts', '.tsx'] }],
    'prettier/prettier': [
      'error',
      {
        semi: true,
        singleQuote: true,
      },
    ],
    'default-param-last': 0,
    'no-param-reassign': [
      'error',
      {
        props: true,
        ignorePropertyModificationsFor: ['state'],
      },
    ],
  },
};
```

## JavaScript 基础规范

### 1. 变量声明

#### 1.1 使用 const 和 let
```javascript
// bad
var foo = 1;
var bar = 2;

// good
const foo = 1;
const bar = 2;
```

#### 1.2 优先使用 const
```javascript
// bad
let count = 1;
if (true) {
  count += 1;
}

// good
const count = 1;
if (true) {
  count += 1;
}
```

### 2. 对象

#### 2.1 使用字面量语法创建对象
```javascript
// bad
const item = new Object();

// good
const item = {};
```

#### 2.2 使用计算属性名
```javascript
function getKey(k) {
  return `a key named ${k}`;
}

// bad
const obj = {
  id: 5,
  name: 'San Francisco',
};
obj[getKey('enabled')] = true;

// good
const obj = {
  id: 5,
  name: 'San Francisco',
  [getKey('enabled')]: true,
};
```

#### 2.3 使用对象方法简写
```javascript
// bad
const atom = {
  value: 1,
  addValue: function (newValue) {
    return atom.value + newValue;
  },
};

// good
const atom = {
  value: 1,
  addValue(newValue) {
    return atom.value + newValue;
  },
};
```

#### 2.4 使用属性简写
```javascript
const lukeSkywalker = 'Luke Skywalker';

// bad
const obj = {
  lukeSkywalker: lukeSkywalker,
};

// good
const obj = {
  lukeSkywalker,
};
```

#### 2.5 使用扩展运算符而非 Object.assign
```javascript
// bad
const original = { a: 1, b: 2 };
const copy = Object.assign({}, original, { c: 3 });

// good
const original = { a: 1, b: 2 };
const copy = { ...original, c: 3 };
const { a, ...noA } = copy; // noA => { b: 2, c: 3 }
```

### 3. 数组

#### 3.1 使用字面量语法创建数组
```javascript
// bad
const items = new Array();

// good
const items = [];
```

#### 3.2 使用 Array#push 添加元素
```javascript
const someStack = [];

// bad
someStack[someStack.length] = 'abracadabra';

// good
someStack.push('abracadabra');
```

#### 3.3 使用扩展运算符复制数组
```javascript
// bad
const len = items.length;
const itemsCopy = [];
let i;

for (i = 0; i < len; i += 1) {
  itemsCopy[i] = items[i];
}

// good
const itemsCopy = [...items];
```

### 4. 解构

#### 4.1 使用对象解构
```javascript
// bad
function getFullName(user) {
  const firstName = user.firstName;
  const lastName = user.lastName;

  return `${firstName} ${lastName}`;
}

// good
function getFullName(user) {
  const { firstName, lastName } = user;
  return `${firstName} ${lastName}`;
}

// best
function getFullName({ firstName, lastName }) {
  return `${firstName} ${lastName}`;
}
```

#### 4.2 使用数组解构
```javascript
const arr = [1, 2, 3, 4];

// bad
const first = arr[0];
const second = arr[1];

// good
const [first, second] = arr;
```

### 5. 字符串

#### 5.1 使用单引号
```javascript
// bad
const name = "Capt. Janeway";

// good
const name = 'Capt. Janeway';
```

#### 5.2 使用模板字符串
```javascript
// bad
function sayHi(name) {
  return 'How are you, ' + name + '?';
}

// good
function sayHi(name) {
  return `How are you, ${name}?`;
}
```

### 6. 函数

#### 6.1 使用函数声明而非函数表达式
```javascript
// bad
const foo = function () {
  // ...
};

// good
function foo() {
  // ...
}
```

#### 6.2 使用默认参数
```javascript
// bad
function handleThings(opts) {
  opts = opts || {};
  // ...
}

// good
function handleThings(opts = {}) {
  // ...
}
```

#### 6.3 使用 rest 参数而非 arguments
```javascript
// bad
function concatenateAll() {
  const args = Array.prototype.slice.call(arguments);
  return args.join('');
}

// good
function concatenateAll(...args) {
  return args.join('');
}
```

### 7. 箭头函数

#### 7.1 使用箭头函数处理匿名函数
```javascript
// bad
[1, 2, 3].map(function (x) {
  const y = x + 1;
  return x * y;
});

// good
[1, 2, 3].map((x) => {
  const y = x + 1;
  return x * y;
});
```

#### 7.2 省略不必要的括号
```javascript
// bad
[1, 2, 3].map((x) => x * x);

// good
[1, 2, 3].map(x => x * x);
```

## React 开发规范

### 1. 组件命名

#### 1.1 文件命名
- 使用 PascalCase：`HelloWorld.tsx`
- React 组件文件使用 `.tsx` 扩展名

#### 1.2 组件导入命名
```javascript
// bad
import reservationCard from './ReservationCard';

// good
import ReservationCard from './ReservationCard';
```

### 2. 组件结构

#### 2.1 使用 Fragment 避免额外的 DOM 节点
```javascript
// bad
const InfoText = () => {
  return (
    <div>
      <h1>Welcome!</h1>
      <p>This our new page, we're glad you're are here!</p>
    </div>
  )
}

// good
const InfoText = () => {
  return (
    <>
      <h1>Welcome!</h1>
      <p>This our new page, we're glad you're are here!</p>
    </>
  )
}
```

#### 2.2 提取组件而非内联函数
```javascript
// bad
function Component() {
  function renderHeader() {
    return <header>...</header>
  }
  return <div>{renderHeader()}</div>
}

// good
import Header from '@modules/common/components/Header'

function Component() {
  return (
    <div>
      <Header />
    </div>
  )
}
```

### 3. 条件渲染

#### 3.1 使用组件而非三元运算符
```javascript
// bad
isSubscribed ? (
  <ArticleRecommendations />
) : isRegistered ? (
  <SubscribeCallToAction />
) : (
  <RegisterCallToAction />
)

// good
function CallToActionWidget({ subscribed, registered }) {
  if (subscribed) {
    return <ArticleRecommendations />
  }
  if (registered) {
    return <SubscribeCallToAction />
  }
  return <RegisterCallToAction />
}

function Component() {
  return (
    <CallToActionWidget
      subscribed={subscribed}
      registered={registered}
    />
  )
}
```

### 4. Hooks 使用规范

#### 4.1 useEffect 必须提供依赖数组
```javascript
// bad
useEffect(() => {
  console.log("hello kitty")
});

// good
useEffect(() => {
  console.log("hello kitty")
}, []);
```

#### 4.2 清理 EventListener
```javascript
const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: undefined,
    height: undefined,
  });
  
  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }
    
    window.addEventListener('resize', handleResize);
    handleResize();
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return windowSize;
}
```

#### 4.3 useState 顺序
```javascript
// bad
function App() {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    console.log("component is mounted");
  }, []);
  
  const [name, setName] = useState('');
  return <h1>React component order</h1>;
}

// good
function App() {
  const [user, setUser] = useState(null);
  const [name, setName] = useState('');
  
  useEffect(() => {
    console.log("component is mounted");
  }, []);
  
  return <h1>React component order</h1>;
}
```

### 5. 性能优化

#### 5.1 使用 React.lazy 进行代码分割
```javascript
// bad
import OtherComponent from './OtherComponent';

// good
const OtherComponent = React.lazy(() => import('./OtherComponent'));
```

#### 5.2 避免在 render 中创建对象
```javascript
// bad
const Comp = () => {
  const data = useMemo(() => ({ type: 'xxx' }), []);
  return <Child data={data}>
}

// good
const data = { type: 'xxx' };
const Comp = () => {
  return <Child data={data}>
}
```

#### 5.3 使用 useMemoizedFn 替代 useCallback
```javascript
const [count, setCount] = useState(0);

const callbackFn = useCallback(() => {
  console.log('123')
}, []);

// bad - 依赖 count 会导致重新渲染
const callbackFnDep = useCallback(() => {
  console.log('count', count)
}, [count]);

// good - 使用 useMemoizedFn
const memoizedFn = useMemoizedFn(() => {
  message.info(`Current count is ${count}`);
});

return (
  <>
    <ExpensiveTree showCount={callbackFn} />
    <ExpensiveTree showCount={memoizedFn} />
  </>
);
```

### 6. 安全规范

#### 6.1 XSS 防护
```javascript
import DOMPurify from 'dompurify';

render(){
  <div
    dangerouslySetInnerHTML={{
      __html: DOMPurify.sanitize(htmlContent)
    }}
  />
}
```

### 7. 代码质量

#### 7.1 避免使用 any 类型
- 优先使用具体类型定义
- 必要时使用联合类型或泛型

#### 7.2 组件 Props 解构
```javascript
// bad
function Input(props) {
  return <input value={props.value} onChange={props.onChange} {...props}/>
}

// good
function Component({ value, onChange }) {
  const [state, setState] = useState('')
  return <div>...</div>
}
```

#### 7.3 避免直接修改 props
```javascript
// bad
function FatherComponent() {
  const [name, setName] = useState('andy')
  return <SonComponent name={name}/>
}

// good
function SonComponent({ name }) {
  return <div>{name}</div>
}
```

## Code Review 规范

### 基本原则
1. **功能正确性**：代码是否实现了预期功能
2. **代码质量**：是否遵循编码规范和最佳实践
3. **性能考虑**：是否存在性能问题
4. **安全性**：是否存在安全隐患
5. **可维护性**：代码是否易于理解和维护

### Review 要点
- 检查是否有重复代码（Duplicated Code）
- 验证变量命名是否语义化
- 确认是否正确使用了可选链操作符（?.）
- 检查是否使用了合适的枚举替代魔法数字
- 验证 React key 的正确使用
- 确认 useEffect 的依赖数组是否正确

### 注释规范
- TODO 注释必须说明具体要做什么
- 删除无用的注释代码
- 重要逻辑必须添加注释说明

## 工具配置

### Prettier 配置
```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5"
}
```

### TypeScript 配置要点
- 启用严格模式
- 配置路径映射
- 设置合适的编译目标

本规范将持续更新，以适应技术发展和项目需求变化。
