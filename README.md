This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

- install IDE plugins (vscode)

  ```bash
  eslint
  prettier
  scss intelliSense
  Tailwind CSS IntelliSense
  DotENV
  ```

- add .env file and set variables value

- run the development server:

  ```bash
  npm install
  npm run dev
  ```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/**/*.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

Other Tools

- [Shadcn Documentation](https://ui.shadcn.com/) - learn about shadcn ui
- [Icons](https://lucide.dev/icons/) - used by shadcn-ui for default mode
- [Icons](https://heroicons.com/outline) - Use it for this repository. It includes more types: outline, solid, etc.
- [Tailwind CSS - en](https://tailwindcss.com/)
- [Tailwind CSS - cn](https://www.tailwindcss.cn/docs/installation)
