import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer'

/** @type {import('next').NextConfig} */
// const isProd = process.env.NODE_ENV === 'production'
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // 忽略 eslint 检查
  },
  typescript: {
    ignoreBuildErrors: true, // 忽略 TypeScript 检查
  },
  experimental: {},
  reactStrictMode: false,
  swcMinify: false, // for pdf-react
  poweredByHeader: false,
  compress: true,
  productionBrowserSourceMaps: false,
  images: {
    loader: 'default',
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // 因为要展示外部网站的icon, 但这不是推荐的做法
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '**', // 因为要展示外部网站的icon, 但这不是推荐的做法
        port: '',
        pathname: '/**',
      },
    ],
  },
  // assetPrefix: isProd ? 'https://dtu1dde5zsodg.cloudfront.net' : '',
  webpack: (config, { dev }) => {
    config.resolve.alias.canvas = false
    // 添加 BundleAnalyzerPlugin 插件配置
    if (process.env.ANALYZE === 'true') {
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'server',
          openAnalyzer: true,
        }),
      )
    }

    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    }

    if (!dev) {
      config.devtool = false // 禁用 source map 生成
    }

    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    })

    return config
  },
}

export default nextConfig
