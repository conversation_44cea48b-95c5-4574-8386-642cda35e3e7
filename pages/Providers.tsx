import React from 'react'
import { I18nextProvider } from 'react-i18next'
import { getI18n, SupportedLangs } from '../src/i18n'
import { ThemeProvider } from '@/context/ThemeContext'

export interface ProvidersProps {
  lang: SupportedLangs
  children: React.ReactNode
}

const Providers: React.FC<ProvidersProps> = ({ lang, children }) => {
  return (
    <I18nextProvider i18n={getI18n(lang)}>
      <ThemeProvider>{children}</ThemeProvider>
    </I18nextProvider>
  )
}

export default Providers
