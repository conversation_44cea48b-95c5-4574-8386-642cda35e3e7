import Login from '@/pages/accounts/login'
import { getUser } from '@/api/getUser'
import { clearCookies } from '@/lib/auth'
import { getCookie } from '@/lib/cookie'
import { smartiesRoutes } from '@/routers'
import { GetServerSideProps } from 'next'

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const localToken = getCookie('smartToken', ctx)
  if (localToken) {
    try {
      const userData = await getUser({
        token: localToken,
      })
      console.log('server login success', userData)
      if (userData.userId) {
        return {
          redirect: {
            destination: smartiesRoutes.basicSearch.home,
            permanent: false,
          },
        }
      }
    } catch (error) {
      console.log('server login failed', error)
      clearCookies(ctx)
    }
  }

  return {
    props: {},
  }
}

export default Login
