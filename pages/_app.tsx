import { AppContext, AppProps } from 'next/app'
import Head from 'next/head'
import Providers from './Providers'
import { getCookie } from '@/lib/cookie'
import { useEffect } from 'react'
import '../src/styles/globals.scss'
import '@radix-ui/themes/styles.css'
import { Theme } from '@radix-ui/themes'
import { getUser } from '@/api/getUser'
import { Roboto } from 'next/font/google'
import { useRouter } from 'next/router'
import { useUserStore } from '@/store/userStore'
import { serverLog } from '@/lib/log'
import { SupportedLangs } from '@/i18n'
import { Toaster } from '@/components/ui/sonner'
import GlobalErrorBlock from '@/components/ui/globalerrorblock'
import ErrorBoundary from '@/components/ui/errorboudary'
import ErrorBasic from '@/components/ui/error-basic'
import { Tracking } from '@/lib/tracking'

const roboto = Roboto({
  subsets: ['latin'],
  variable: '--font-roboto',
  weight: ['400', '500', '700', '900'],
})

const SmartiesApp = ({ Component, pageProps, lang }: AppProps & { lang?: SupportedLangs }) => {
  const router = useRouter()
  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  useEffect(() => {
    const noNeedLogin = [
      '/',
      '/_error',
      '/login',
      '/logout',
      '/maintenance',
      '/recover',
      '/signup',
      '/password/reset',
    ].includes(router.pathname)

    if (!user && !noNeedLogin) {
      getUser().then((data) => {
        if (data.userId) {
          updateUser(data)
        }
      })
    }
  }, [user, router])

  // 注册 sw
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          // 强制更新 Service Worker
          registration.update()
          console.log('service worker registe successfully', registration)
        })
        .catch((error) => {
          console.error('service worker registe failed:', error)
        })
    }
  }, [])

  useEffect(() => {
    if (
      typeof window !== 'undefined' &&
      process.env.NEXT_PUBLIC_ENV &&
      process.env.NEXT_PUBLIC_ENV !== 'development'
    ) {
      // Initialize the auto-tracking system and tracking highlight feature
      Tracking.initAutoTracking()

      // Explicitly initialize the tracking highlight feature to ensure it's available on all pages
      Tracking.initTrackingHighlight()

      console.log('Tracking systems initialized')
    }
  }, [])

  return (
    <>
      <Head>
        {['/login', '/signup', '/password/reset'].includes(router.pathname) ? (
          <meta name='viewport' content='width=device-width, initial-scale=1.0' />
        ) : (
          <meta name='viewport' content='width=1280, initial-scale=1.0' />
        )}
        <title>AI Smarties | Business research assistant</title>
        <link rel='icon' type='image/svg+xml' href='/images/logo.svg' />
      </Head>
      <main className={roboto.variable}>
        <Theme>
          <Providers lang={(user?.setting.language as SupportedLangs) ?? lang}>
            <GlobalErrorBlock />
            <ErrorBoundary fallback={<ErrorBasic />}>
              <Component {...pageProps} />
            </ErrorBoundary>
            <Toaster />
          </Providers>
        </Theme>
      </main>
    </>
  )
}

SmartiesApp.getInitialProps = async ({
  Component,
  ctx,
  router,
}: AppContext): Promise<AppProps & { lang?: SupportedLangs }> => {
  let pageProps = {}
  if (Component.getInitialProps) {
    pageProps = await Component.getInitialProps(ctx)
  }
  serverLog('getInitialProps from _app.tsx', {
    asPath: ctx.asPath,
    pathname: ctx.pathname,
  })

  // 从 cookie 中获取 lang
  const lang = getCookie('lang', ctx) ? (getCookie('lang', ctx) as SupportedLangs) : undefined

  return {
    lang,
    pageProps,
    Component,
    router,
  }
}

export default SmartiesApp
