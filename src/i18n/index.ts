import { initReactI18next } from 'react-i18next'
import type { Resource } from 'i18next'
import i18n from 'i18next'
import zh from './locales/zh'
import en from './locales/en'
import ja from './locales/ja'
import fr from './locales/fr'
import de from './locales/de'

export enum Namespace {
  GLOBAL = 'global',
  LOGIN = 'login',
  BASIC_SEARCH = 'basicSearch',
  HOME = 'home',
  PRICING = 'pricing',
  SUBSCRIPTION = 'subscription',
  DEEPEXPLORE = 'deepExplore',
  GENERATOR = 'generator',
  DEEPRESEARCH = 'deepResearch',
}
export enum SupportedLangs {
  EN = 'en',
  ZH = 'zh',
  JA = 'ja',
  FR = 'fr',
  DE = 'de',
}
const resources: Resource = {
  en,
  zh,
  ja,
  fr,
  de,
}
const ns = Object.values(Namespace)

export function getI18n(lng: SupportedLangs) {
  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources,
      fallbackLng: SupportedLangs.EN,
      ns,
      defaultNS: Namespace.GLOBAL,
      interpolation: {
        escapeValue: false,
      },
      returnNull: false,
      lng,
    })

  return i18n
}
