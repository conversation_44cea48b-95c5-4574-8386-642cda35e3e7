import { fetchWithAuth } from '@/lib/fetch'
import { AuthError } from './types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface LoginEmailRequest {
  email: string
  password: string
}

export interface LoginEmailResponse {
  userId?: string
  accessToken: string
}

export const loginEmail = (data: LoginEmailRequest) => {
  return fetchWithAuth<LoginEmailResponse | AuthError>(`${apiUrl}/auth/loginEmail`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}
