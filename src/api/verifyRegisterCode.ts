import { fetchWithAuth } from '@/lib/fetch'
import { AuthError } from './types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface VerifyRegisterCodeRequest {
  code: string
}

export interface VerifyRegisterCodeResponse {
  accessToken: string
}

export const verifyRegisterCode = (data: VerifyRegisterCodeRequest) => {
  return fetchWithAuth<VerifyRegisterCodeResponse | AuthError>(
    `${apiUrl}/auth/verifyRegisterCode`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )
}
