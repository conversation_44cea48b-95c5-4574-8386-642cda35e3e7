import { fetchWithAuth } from '@/lib/fetch'
import { MessageType, ModelEnum, PoTypeEnum } from '@/types/index'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface StartQuestRequest {
  message: MessageType
  poType: PoTypeEnum
  model?: ModelEnum
  sessionId?: string
  inline?: boolean
  useReasoning?: boolean
}
export interface StartQuestResponse {
  processId: string
  sessionId: string
}

export const startQuest = async (data: StartQuestRequest): Promise<StartQuestResponse> => {
  const response = await fetchWithAuth<StartQuestResponse>(`${apiUrl}/process/start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: ModelEnum.CLAUDE,
      ...data,
    }),
  })

  return response
}
