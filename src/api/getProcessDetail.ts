import { fetchWithAuth } from '@/lib/fetch'
import { ProcessType } from './getInProgressTask'
import { ReferenceItem } from './getSession'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface GetProcessDetailRequest {
  processId: string
}

export interface GetProcessDetailResponse {
  process?: ProcessType | null
  referenceList?: ReferenceItem[]
}

export const getProcessDetail = async (
  data: GetProcessDetailRequest,
): Promise<GetProcessDetailResponse> => {
  const response = await fetchWithAuth<GetProcessDetailResponse>(`${apiUrl}/process/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
