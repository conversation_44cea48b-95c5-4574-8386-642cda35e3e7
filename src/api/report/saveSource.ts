import { fetchWithAuth } from '@/lib/fetch'
import { MessageRoleEnum } from '../getSession'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface ChatMessageItem {
  role: MessageRoleEnum
  content: string
  processId: string
}

export interface MediaMessageItem {
  role: MessageRoleEnum
  content: string
  mediaProcessId: string
}

export interface PdfMessageItem {
  role: MessageRoleEnum
  content: string
  pdfProcessId: string
}

export enum SourceTypeEnum {
  CHAT = 'CHAT',
  MEDIA = 'MEDIA',
  PDF = 'PDF',
}

export type MediaSourceResponse = {
  id: string
  type: SourceTypeEnum
  messages: { content: string; role: string; mediaProcessId: string }[]
  mediaId: string
  url: string
  sessionId: string
}

export type ChatSourceResponse = {
  id: string
  type: SourceTypeEnum
  messages: { content: string; role: string; processId: string }[]
  sessionId: string
}

export type PdfSourceResponse = {
  id: string
  type: SourceTypeEnum
  messages: { content: string; role: string; pdfProcessId: string }[]
  pdfId: string
  url: string
  title: string
  snippet?: string
  sessionId: string
}

export const saveChatSource = async (
  data: ChatMessageItem[],
  sessionId: string,
  quickSave: boolean = false,
): Promise<ChatSourceResponse> => {
  const response = await fetchWithAuth<ChatSourceResponse>(`${apiUrl}/report/saveChatSource`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      messages: data,
      sessionId,
      quickSave,
    }),
  })

  return response
}

export const saveMediaSource = async (
  data: MediaMessageItem[],
  mediaId: string,
  quickSave: boolean = false,
  source?: string,
): Promise<MediaSourceResponse> => {
  const response = await fetchWithAuth<MediaSourceResponse>(`${apiUrl}/report/saveMediaSource`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      messages: data,
      mediaId,
      quickSave,
      source,
    }),
  })

  return response
}

export const savePdfSource = async (
  data: PdfMessageItem[],
  pdfId: string,
  quickSave: boolean = false,
  source?: string,
): Promise<PdfSourceResponse> => {
  const response = await fetchWithAuth<PdfSourceResponse>(`${apiUrl}/report/savePdfSource`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      messages: data,
      pdfId,
      quickSave,
      source,
    }),
  })

  return response
}
