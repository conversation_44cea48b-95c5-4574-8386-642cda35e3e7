import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface ChangeSubscriptionRequest {
  subscriptionId: string
  newPriceId: string
  ifNow: boolean
}

export interface ChangeSubscriptionResponse {
  result?: 'ok'
}

export const changeSubscription = async (
  data: ChangeSubscriptionRequest,
): Promise<ChangeSubscriptionResponse> => {
  const response = await fetchWithAuth<ChangeSubscriptionResponse>(
    `${apiUrl}/subscription/changePlan`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )

  return response
}
