import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface CancelSubscriptionRequest {
  subscriptionId: string
}

export interface CancelSubscriptionResponse {
  result?: 'ok'
}

export const cancelSubscription = async (
  data: CancelSubscriptionRequest,
): Promise<CancelSubscriptionResponse> => {
  const response = await fetchWithAuth<CancelSubscriptionResponse>(
    `${apiUrl}/subscription/cancel`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )

  return response
}
