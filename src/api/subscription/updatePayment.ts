import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface UpdatePaymentRequest {
  subscriptionId: string
}

export interface UpdatePaymentResponse {
  id: string
}

export const updatePayment = async (data: UpdatePaymentRequest): Promise<UpdatePaymentResponse> => {
  const response = await fetchWithAuth<UpdatePaymentResponse>(
    `${apiUrl}/subscription/getUpdatePaymentMethodTransaction`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )

  return response
}
