import { fetchWithAuth } from '@/lib/fetch'
import { SubscriptionEnum } from '@/types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export type PaymentCardType =
  | 'american_express'
  | 'diners_club'
  | 'discover'
  | 'jcb'
  | 'mada'
  | 'maestro'
  | 'mastercard'
  | 'union_pay'
  | 'unknown'
  | 'visa'
export type PaymentType =
  | 'alipay'
  | 'apple_pay'
  | 'bancontact'
  | 'card'
  | 'google_pay'
  | 'ideal'
  | 'offline'
  | 'paypal'
  | 'unknown'
  | 'wire_transfer'

export interface IPaymentMethod {
  type: PaymentType
  card: {
    type: PaymentCardType
    last4: string
  } | null
}

export type SubscriptionStatus = 'active' | 'past_due'

export interface GetSubscriptionDetailResponse {
  subscriptionId: string
  currentPlan: SubscriptionEnum
  paymentMethod: IPaymentMethod
  nextBilledAt: number | null
  curPeriodEndsAt: number | null
  status: SubscriptionStatus
}

export const getSubscriptionDetail = async (): Promise<GetSubscriptionDetailResponse> => {
  const response = await fetchWithAuth<GetSubscriptionDetailResponse>(
    `${apiUrl}/subscription/getPlan`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )

  return response
}
