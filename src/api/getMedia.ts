import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum MediaRetrieverType {
  IMAGE = 'image',
  VIDEO = 'video',
}

export interface GetMediaRequest {
  processId: string
  type: MediaRetrieverType
  ifAll: boolean
}

export interface MediaItemType {
  mediaId: string
  mediaUrl?: string
  siteUrl?: string
  s3Url?: string
}

export type GetMediaResponse = Array<MediaItemType>

export const getMedia = async (data: GetMediaRequest): Promise<GetMediaResponse> => {
  const response = await fetchWithAuth<GetMediaResponse>(`${apiUrl}/process/getMedia`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
