import { fetchWithAuth } from '@/lib/fetch'
import { MessageRoleEnum } from '../getSession'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface MediaMessageType {
  role: MessageRoleEnum
  content: string
  timestamp: number
  mediaProcessId: string
  reportSourceId?: string
}

export interface MediaSessionType {
  mediaSessionId: string
  userId: string
  mediaId: string
  createdAt: number
  updatedAt: number
  messages: MediaMessageType[]
  isDeleted: boolean
  parentSessionId: string
}

export interface ImageSessionRequest {
  mediaId: string
}

export interface ImageSessionResponse {
  mediaSession: MediaSessionType
}

export const imageSession = async (data: ImageSessionRequest): Promise<ImageSessionResponse> => {
  const response = await fetchWithAuth<ImageSessionResponse>(`${apiUrl}/media/imageSession`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
