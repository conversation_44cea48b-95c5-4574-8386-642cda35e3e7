import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum QuickImageInquiryEnum {
  DESCRIBE = 'DESCRIBE',
}
export interface ImageStartRequest {
  mediaId: string
  mediaSessionId?: string
  message?: string
  quickInquiry?: QuickImageInquiryEnum
}
export interface ImageStartResponse {
  mediaSessionId: string
  mediaProcessId: string
}

export const imageStart = async (data: ImageStartRequest): Promise<ImageStartResponse> => {
  const response = await fetchWithAuth<ImageStartResponse>(`${apiUrl}/media/imageStart`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
