import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum ImageStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  FINISH = 'FINISH',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export interface MediaProcessType {
  mediaProcessId: string
  mediaSessionId: string
  userId: string
  mediaId: string
  status: ImageStatusEnum
  message: string
  result?: string
}

export interface ImageRunningRequest {
  mediaId: string
}

export interface ImageRunningResponse {
  mediaProcess: MediaProcessType
}

export const imageRunning = async (data: ImageRunningRequest): Promise<ImageRunningResponse> => {
  const response = await fetchWithAuth<ImageRunningResponse>(`${apiUrl}/media/imageRunning`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
