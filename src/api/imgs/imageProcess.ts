import { fetchWithAuth } from '@/lib/fetch'
import { MediaProcessType } from './imageRunning'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface ImageProcessRequest {
  mediaProcessId: string
}

export interface ImageProcessResponse {
  mediaProcess: MediaProcessType
}

export const imageProcess = async (data: ImageProcessRequest): Promise<ImageProcessResponse> => {
  const response = await fetchWithAuth<ImageProcessResponse>(`${apiUrl}/media/imageProcess`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
