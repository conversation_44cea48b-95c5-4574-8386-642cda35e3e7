import { fetchWithAuth } from '@/lib/fetch'
import { ImageStatusEnum } from './imageRunning'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface ImageCancelRequest {
  mediaProcessId: string
}

export interface ImageCancelResponse {
  mediaProcess: {
    status: ImageStatusEnum
  }
}

export const imageCancel = async (data: ImageCancelRequest): Promise<ImageCancelResponse> => {
  const response = await fetchWithAuth<ImageCancelResponse>(`${apiUrl}/media/imageCancel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
