import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface UpdateSessionRequest {
  sessionId: string
  title: string
}
export interface UpdateSessionResponse {
  result: 'ok' | string
}

export const updateSession = async (data: UpdateSessionRequest): Promise<UpdateSessionResponse> => {
  const response = await fetchWithAuth<UpdateSessionResponse>(`${apiUrl}/session/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
