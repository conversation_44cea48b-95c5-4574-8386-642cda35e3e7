import { fetchWithAuth } from '@/lib/fetch'
import { ProcessType } from './getInProgressTask'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface CancelProcessRequest {
  processId: string
}
export interface CancelProcessResponse {
  process: ProcessType
}

export const cancelProcess = async (data: CancelProcessRequest): Promise<CancelProcessResponse> => {
  const response = await fetchWithAuth<CancelProcessResponse>(`${apiUrl}/process/cancel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
