import { fetchWithAuth } from '@/lib/fetch'
import { PoTypeEnum } from '@/types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum PDFStatusEnum {
  INIT = 'INIT',
  DOWNLOADING = 'DOWNLOADING',
  DOWNLOAD_SUCCESS = 'DOWNLOAD_SUCCESS',
  DOWNLOAD_FAILED = 'DOWNLOAD_FAILED',
  UPLOADING = 'UPLOADING',
  OCRING = 'OCRING',
  OCR_SUCCESS = 'OCR_SUCCESS',
  OCR_FAILED = 'OCR_FAILED',
  INDEXING = 'INDEXING',
  INDEX_SUCCESS = 'INDEX_SUCCESS',
  INDEX_FAILED = 'INDEX_FAILED',
}

export enum PdfFailedEnum {
  EXCEED_LIMIT = 'context_length_exceeded',
}

export interface GetPdfsRequest {
  processId: string
}

export enum PdfPermissionEnum {
  NORMAL = 'NORMAL',
  OVER_PAGE = 'OVER_PAGE',
  TOO_LARGE = 'TOO_LARGE',
}
export interface PDFItemType {
  pdfId: string
  favicon: string
  snippet: string
  title: string
  url: string
  fileName: string
  status: PDFStatusEnum
  s3Url?: string
  totalPage?: number
  pdfPermission?: PdfPermissionEnum
}

export interface ParentSessionType {
  sessionId: string
  title: string
  poType: PoTypeEnum
}

export type GetPdfsResponse = {
  pdfList: Array<PDFItemType>
  parentSession: ParentSessionType
}

export const getPdfs = async (data: GetPdfsRequest): Promise<GetPdfsResponse> => {
  const response = await fetchWithAuth<GetPdfsResponse>(`${apiUrl}/process/listPdf`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
export const extractProcessPdfsApi = async (data: {
  processId: string
}): Promise<GetPdfsResponse> => {
  const response = await fetchWithAuth<GetPdfsResponse>(`${apiUrl}/process/extractProcessPdfs`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
