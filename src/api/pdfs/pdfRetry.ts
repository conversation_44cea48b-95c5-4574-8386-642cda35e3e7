import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL
export interface PdfRetryRequest {
  pdfId: string
}

export type PdfRetryResponse = {
  result: 'ok'
}

export const pdfRetry = async (data: PdfRetryRequest): Promise<PdfRetryResponse> => {
  const response = await fetchWithAuth<PdfRetryResponse>(`${apiUrl}/pdf/retry`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
