import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface DeletePdfRequest {
  pdfId: string
}

export type DeletePdfResponse = {
  result: 'ok'
}

export const deletePdf = async (data: DeletePdfRequest): Promise<DeletePdfResponse> => {
  const response = await fetchWithAuth<DeletePdfResponse>(`${apiUrl}/pdf/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
