import { fetchWithAuth } from '@/lib/fetch'
import { PDFItemType } from './getPdf'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface UploadUrlRequest {
  url: string
  processId: string
}

export interface UploadUrlResponse {
  pdf: PDFItemType
  errCode?: string
}

export const uploadUrl = async (data: UploadUrlRequest): Promise<UploadUrlResponse> => {
  const response = await fetchWithAuth<UploadUrlResponse>(`${apiUrl}/pdf/uploadUrl`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
