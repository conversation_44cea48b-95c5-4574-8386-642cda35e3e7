import { fetchWithAuth } from '@/lib/fetch'
import { PreviewProps } from '@/pages/deep-explore/file-preview/Preview'

const apiUrl = process.env.NEXT_PUBLIC_API_URL
export interface UploadFilesRequest {
  processId: string
  s3Key: string
}

export type UploadFilesResponse = {
  pdf: {
    pdfId: string
    favicon: string
    snippet: string
    title: string
    s3Url: string
  }
}

export const uploadFiles = async (data: UploadFilesRequest): Promise<UploadFilesResponse> => {
  const response = await fetchWithAuth<UploadFilesResponse>(`${apiUrl}/pdf/uploadFile`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
export const uploadFileApi = async (data: {
  fileName: string
  s3Key: string
}): Promise<UploadFilesResponse> => {
  const response = await fetchWithAuth<UploadFilesResponse>(`${apiUrl}/deepExplore/uploadFile`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
export const listFileApi = async (data: {
  sort?: 'ASC' | 'DESC'
  pageSize: number
  lastPageKey?: string
}): Promise<UploadFilesResponse> => {
  const response = await fetchWithAuth<UploadFilesResponse>(`${apiUrl}/deepExplore/listFile`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
// 获取文件总大小
export const getFileTotalSizeApi = async (): Promise<{ size: number }> => {
  const response = await fetchWithAuth<{ size: number }>(`${apiUrl}/deepExplore/getTotalSize`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
// 删除文件
export const deleteFileApi = async (data: { fileIds: string[] }): Promise<void> => {
  await fetchWithAuth(`${apiUrl}/deepExplore/deleteFile`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
}
// 获取详情
export const getDetailsApi = async (data: { fileId: string }): Promise<PreviewProps> => {
  const response: PreviewProps = await fetchWithAuth(`${apiUrl}/deepExplore/getFile`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  return response
}
