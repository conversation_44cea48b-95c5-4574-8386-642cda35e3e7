import { fetchWithAuth } from '@/lib/fetch'
import { MessageRoleEnum } from '../getSession'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface PdfSessionRequest {
  pdfId: string
}

export interface PdfMessageItem {
  role: MessageRoleEnum
  content: string
  pdfProcessId: string
  timestamp?: string
  reportSourceId?: string
}
export type PdfSessionResponse = {
  pdfSession: {
    pdfSessionId: string
    pdfId: string
    messages: PdfMessageItem[]
  }
}

export const pdfSession = async (data: PdfSessionRequest): Promise<PdfSessionResponse> => {
  const response = await fetchWithAuth<PdfSessionResponse>(`${apiUrl}/pdf/getSession`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
