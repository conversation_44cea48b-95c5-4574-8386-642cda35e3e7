import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface BatchDownloadPdfRequest {
  processId: string
}

export type BatchDownloadPdfResponse = {
  result: 'ok'
}

export const batchDownloadPdf = async (
  data: BatchDownloadPdfRequest,
): Promise<BatchDownloadPdfResponse> => {
  const response = await fetchWithAuth<BatchDownloadPdfResponse>(
    `${apiUrl}/process/batchDownloadPdfs`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )

  return response
}
