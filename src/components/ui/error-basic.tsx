import { Namespace } from "@/i18n";
import { smartiesRoutes } from "@/routers";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { Text, TextEnum } from "../business/text";
import { Button } from "./button";

const ErrorBasic = () => {
  const { t } = useTranslation([Namespace.GLOBAL])

  const redirectToBasicsearch = () => {
    window.location.href = smartiesRoutes.basicSearch.home;
  }

  return (
    <div className="h-screen w-screen flex justify-center items-center">
      <div className="flex">
        <div><Image src={'/images/error_occur.png'} alt="error occur" width={122} height={122} /></div>
        <div className="ml-8">
          <div className="mt-1 h-14 text-lg font-medium">
            <div>
              <Text type={TextEnum.H4}>{t('global:display.errorOccur')}</Text>
            </div>
            <div>
              <Text type={TextEnum.H4}>{t('global:display.refreshRequest')}</Text>
            </div>
          </div>
          <div className="mt-4">
            <Button onClick={redirectToBasicsearch}>
              <Image src={'/images/refresh.svg'} alt="refresh icon" width={16} height={16} />
              <span className="ml-1">
                <Text type={TextEnum.Body_big}>{t('global:display.refresh')}</Text>
              </span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ErrorBasic;