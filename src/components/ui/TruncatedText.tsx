import React, { useRef, useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip'

interface TruncatedTextProps {
  text: string
  className?: string
  maxLines?: number
  children?: React.ReactNode
  tooltipClassName?: string
  tooltipMaxWidth?: string
  delayDuration?: number
  side?: 'top' | 'bottom' | 'left' | 'right'
  showTooltipAlways?: boolean // 是否总是显示 tooltip，不检查截断
}

/**
 * 带 Tooltip 的文本截断组件
 * 当文本被截断时，hover 显示完整内容
 */
const TruncatedText: React.FC<TruncatedTextProps> = ({
  text,
  className = '',
  maxLines = 1,
  children,
  tooltipClassName = '',
  tooltipMaxWidth = '320px',
  delayDuration = 300,
  side = 'top',
  showTooltipAlways = false,
}) => {
  const textRef = useRef<HTMLDivElement>(null)
  const [isTruncated, setIsTruncated] = useState(false)

  useEffect(() => {
    const checkTruncation = () => {
      if (textRef.current) {
        const element = textRef.current
        // 检查是否发生了文本截断
        const isOverflowing = element.scrollHeight > element.clientHeight ||
                             element.scrollWidth > element.clientWidth
        setIsTruncated(isOverflowing)
      }
    }

    // 如果设置了总是显示 tooltip，则不需要检查截断
    if (showTooltipAlways) {
      setIsTruncated(true)
      return
    }

    // 延迟检查，确保元素已经渲染完成
    const timeoutId = setTimeout(checkTruncation, 0)

    // 监听窗口大小变化
    window.addEventListener('resize', checkTruncation)
    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', checkTruncation)
    }
  }, [text, showTooltipAlways])

  const content = children || text
  const lineClampClass = maxLines === 1 ? 'line-clamp-1' : `line-clamp-${maxLines}`

  // 如果没有截断且不强制显示 tooltip，直接返回内容
  if (!isTruncated && !showTooltipAlways) {
    return (
      <div
        ref={textRef}
        className={`${className} ${lineClampClass}`}>
        {content}
      </div>
    )
  }

  // 如果有截断或强制显示 tooltip，包装 Tooltip
  return (
    <TooltipProvider delayDuration={delayDuration}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            ref={textRef}
            className={`${className} ${lineClampClass} transition-colors duration-200`}>
            {content}
          </div>
        </TooltipTrigger>
        <TooltipContent
          side={side}
          className={`break-words bg-gray-900 text-white border-gray-700 shadow-lg ${tooltipClassName}`}
          style={{ maxWidth: tooltipMaxWidth }}
          sideOffset={8}>
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {text}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export default TruncatedText
