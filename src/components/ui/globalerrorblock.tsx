import { Namespace } from "@/i18n";
import { useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

const GlobalErrorBlock = () => {
  const { t } = useTranslation([Namespace.GLOBAL]);

  const showErrorToast = useCallback(() => {
    toast.error(t("global:error.systemError"), {
      duration: 3000,
    });
  }, [t]);

  useEffect(() => {
    const handleError = () => {
      showErrorToast();
    };

    const handlePromiseRejection = () => {
      showErrorToast();
    };

    window.addEventListener("error", handleError);
    window.addEventListener("unhandledrejection", handlePromiseRejection);

    return () => {
      window.removeEventListener("error", handleError);
      window.removeEventListener("unhandledrejection", handlePromiseRejection);
    };
  }, [showErrorToast]);

  return null;
};

export default GlobalErrorBlock;
