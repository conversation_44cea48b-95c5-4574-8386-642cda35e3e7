import { Text, TextEnum } from '@/components/business/text';
import { Namespace } from "@/i18n";
import { XMarkIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";
import Image from "next/image";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { toast as sonnerToast } from "sonner";

const icons: { [key: string]: ReactNode } = {
  success: <Image src="/images/success.svg" width={16} height={16} alt="Success icon" />,
  error: <Image src="/images/error.svg" width={16} height={16} alt="Error icon" />,
  warning: <Image src="/images/warning.svg" width={16} height={16} alt="Warning icon" />,
  info: null,
};

type ToastType = "success" | "error" | "warning" | "info";

interface ToastOptions {
  duration?: number;
  position?: "top-left" | "top-center" | "top-right" | "bottom-left" | "bottom-center" | "bottom-right";
}

export function useToast() {
  const { t } = useTranslation(Namespace.GLOBAL)
  
  const showToast = (content: string | ReactNode | null, type: ToastType = "info", options?: ToastOptions) => {
    // 创建一个自定义的 toast 内容，包含关闭按钮
    const toastContent = (
      typeof content === 'string' ?
      (<div>
        <XMarkIcon width={16} height={16} className="absolute top-4 right-4 cursor-pointer"
          onClick={() => sonnerToast.dismiss(toastId)} // 使用 toastId 关闭当前的 toast
        />
        {type !== "info" && <div className="flex items-center">
          <div className="flex items-center">
            <div>{icons[type]}</div>
            <div className={clsx("ml-2", type === "error" ? "text-dangerous" : type === 'warning' ? "text-warning" : "")}>
              <Text type={TextEnum.Body_big}>{t(type === "error" ? "status.error" : type === 'warning' ? "status.warning" : "status.success")}</Text>
            </div>
          </div>
        </div>}
        {content && <div className={clsx("text-secondary-black-2", type !== "info" ? 'mt-3' : '')}><span>{content}</span></div>}
      </div>) : content
    );

    // 显示 toast 并传入自定义的内容和样式
    const toastId = sonnerToast(toastContent, {
      style: {
        display: 'block',
        background: "#fff",
        padding: "16px",
        border: "1px solid rgb(228, 229, 236)",
        borderRadius: "8px",
        boxShadow: "0px 4px 6px -4px rgba(0, 0, 0, 0.1),0px 10px 15px -3px rgba(0, 0, 0, 0.1",
      },
      ...options,
    });

    // 返回 toastId，以便手动关闭
    return toastId;
  };

  // 创建一个返回类型方法的对象
  const createToast = (type?: ToastType) => {
    return (content: string | ReactNode | null, options?: ToastOptions) => {
      if (type) {
        showToast(content, type, options);
      } else {
        showToast(content, "info", options); // 默认 type 为 info
      }
    };
  };

  // 返回一个包含通用函数和类型方法的对象
  const toast = Object.assign(createToast(), {
    success: createToast("success"),
    error: createToast("error"),
    warning: createToast("warning"),
    info: createToast("info"),
  });

  return toast;
}
