import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Text, TextEnum } from '@/components/business/text'
import { memo, useCallback } from 'react'
import { ExternalLink, FileText } from 'lucide-react'

interface SrefTriggerProps {
  srefTag: string
}

const isValidUrl = (string: string | URL) => {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

const SrefTrigger = memo(({ srefTag }: SrefTriggerProps) => {
  // 解析sref标签中的属性
  const parseAttributes = useCallback((tag: string) => {
    const urlMatch = /r_url="([^"]*?)"/.exec(tag)
    const titleMatch = /r_title="([^"]*?)"/.exec(tag)
    const contentMatch = /r_content="([^"]*?)"/.exec(tag)

    return {
      url: urlMatch ? urlMatch[1] : '',
      title: titleMatch ? titleMatch[1] : '',
      content: contentMatch ? contentMatch[1] : '',
    }
  }, [])

  const { url, title, content } = parseAttributes(srefTag)

  const openUrl = useCallback(() => {
    if (isValidUrl(url)) {
      window.open(url, '_blank', 'noopener,noreferrer')
    }
  }, [url])

  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span
            className='hover:text-primary-darker ml-1 inline-flex h-4 w-4 -translate-y-0.5 cursor-pointer items-center justify-center rounded-full bg-primary text-[13px] text-white duration-200'
            role='button'
            tabIndex={0}
            aria-label={`Open reference: ${title}`}>
            R
          </span>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          align='start'
          sideOffset={8}
          avoidCollisions={true}
          className='z-[1000] max-w-[400px] rounded-md border border-gray-200 bg-white p-3 shadow-lg'>
          <div className='space-y-2'>
            <div className='flex items-start gap-2'>
              <FileText className='mt-0.5 h-4 w-4 flex-shrink-0 text-primary' />
              <div className='min-w-0 flex-1'>
                <Text
                  type={TextEnum.Body_medium}
                  className='line-clamp-2 break-words font-semibold text-gray-900'>
                  {title}
                </Text>
              </div>
            </div>

            {content && (
              <Text
                type={TextEnum.Body_small}
                className='line-clamp-3 break-words leading-relaxed text-gray-600'>
                {content}
              </Text>
            )}

            <div className='flex items-center gap-1 border-t border-gray-100 pt-1'>
              <ExternalLink className='h-3 w-3 text-gray-400' />
              <Text
                type={TextEnum.Body_small}
                onClick={openUrl}
                className='cursor-pointer truncate text-xs text-gray-500'>
                {url}
              </Text>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
})

SrefTrigger.displayName = 'SrefTrigger'

export default SrefTrigger
