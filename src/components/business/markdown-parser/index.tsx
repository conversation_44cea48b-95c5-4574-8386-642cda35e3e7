import React, { useEffect, useState } from 'react'
import { Marked, Tokens } from 'marked'
import { ReferenceItem } from '@/api/getSession'
import Trigger from './Trigger'
import SrefTrigger from './SrefTrigger'
import ReferenceList from '@/pages/basic-search/chat/ReferenceList'
import parse from 'html-react-parser'
import mermaid from 'mermaid'

interface MarkdownParserProps {
  content: string
  list?: ReferenceItem[]
  onClickPageNumber?: (pageNumber: { pageNumber: number }) => void
}

const customExtensions = [
  {
    name: 'customText',
    level: 'inline',
    start: (src: string) => src.match(/\[\^\d+\^\]/)?.index,
    tokenizer(src: string) {
      const match = /^\[\^(\d+)\^\]/.exec(src)
      return match ? { type: 'customText', raw: match[0], text: match[1] } : false
    },
    renderer: (token: Tokens.Generic) =>
      `<span class='ai-smarties-md-trigger' data-index=${token.text}></span>`,
  },
  {
    name: 'pageNumberText',
    level: 'inline',
    start: (src: string) => src.match(/\[\^\^\d+\^\^\]/)?.index,
    tokenizer(src: string) {
      const match = /^\[\^\^(\d+)\^\^\]/.exec(src)
      return match ? { type: 'pageNumberText', raw: match[0], text: match[1] } : false
    },
    renderer: (token: Tokens.Generic) =>
      `<span class='ai-smarties-md-item' data-pdf-page=${token.text}></span>`,
  },
  {
    name: 'customRefs',
    level: 'inline',
    start: (src: string) => src.indexOf(':::component'),
    tokenizer(src: string) {
      const match = /:::component([\s\S]*?):::/.exec(src)
      return match ? { type: 'customRefs', raw: match[0], text: match[1] } : false
    },
    renderer: (token: Tokens.Generic) =>
      `<div id="react-component-refs" data-page-refs=${encodeURIComponent(token.text.trim())}></div>`,
  },
  {
    name: 'srefText',
    level: 'inline',
    start: (src: string) => {
      const index = src.match(/<sref[^>]*>[\s\S]*?<\/sref>/)?.index
      return index
    },
    tokenizer(src: string) {
      const match = /^<sref[^>]*>[\s\S]*?<\/sref>/.exec(src)
      if (match) {
        return {
          type: 'srefText',
          raw: match[0],
          text: match[0],
        }
      }
      return false
    },
    renderer: (token: Tokens.Generic) => {
      return `<span class='ai-smarties-sref-trigger' data-sref="${encodeURIComponent(token.text)}"></span>`
    },
  },
]

const marked = new Marked({
  extensions: customExtensions,
  gfm: true,
  breaks: true,
  hooks: {
    preprocess: (html) => html,
    postprocess: (html) => html,
  },
  renderer: {
    code({ text, lang }: Tokens.Code) {
      if (lang === 'mermaid') {
        const id = `mermaid-${Math.random().toString(36).slice(2, 9)}`
        return `<div class="mermaid" id="${id}" data-mermaid-code="${encodeURIComponent(text)}"></div>`
      }
      return `<pre><code class="language-${lang}">${text}</code></pre>`
    },
    link: ({ href, title, tokens }: Tokens.Link) =>
      `<a href="${href}" target="_blank" title="${title || ''}" rel="noopener noreferrer">${
        tokens?.map((t) => (t as Tokens.Link).text ?? '').join('') || href
      }</a>`,
  },
})

const MarkdownParser: React.FC<MarkdownParserProps> = ({
  content,
  list = [],
  onClickPageNumber,
}) => {
  const [htmlContent, setHtmlContent] = useState<string | Promise<string>>('')
  const [referenceList, setReferenceList] = useState<ReferenceItem[]>([])
  useEffect(() => {
    if (typeof content === 'string') {
      setHtmlContent(marked.parse(content))
    }
  }, [content])

  useEffect(() => {
    if (htmlContent) {
      try {
        mermaid.initialize({
          startOnLoad: false,
          securityLevel: 'loose',
          theme: 'default',
        })
        const mermaidNodes = document.querySelectorAll('.mermaid')

        mermaidNodes.forEach((node) => {
          const code = decodeURIComponent(node.getAttribute('data-mermaid-code') || '')
          const id = node.id

          if (code && id) {
            mermaid
              .render(`svg-${id}`, code)
              .then(({ svg }) => {
                requestAnimationFrame(() => {
                  node.innerHTML = svg
                })
              })
              .catch((e) => {
                const errorSelectors = [
                  '.mermaid-error-display',
                  '[id^="mermaid-error-"]',
                  '[id^="dsvg-mermaid"]',
                  '.error-text',
                  '.error-icon',
                  '.mermaid > svg > g.error-icon',
                  '.mermaid-wrapper > svg > g.error-icon',
                  '.bomb-error',
                ]
                errorSelectors.forEach((selector) => {
                  const elements = document.querySelectorAll(selector)
                  elements.forEach((el) => {
                    if (el.parentNode) {
                      el.parentNode.removeChild(el)
                    }
                  })
                })
                console.error(`Mermaid render error in node ${id}:`, e)
              })
          }
        })
      } catch (e) {
        console.error('Mermaid render init error:', e)
      }
    }
  }, [htmlContent])

  useEffect(() => {
    const tempReferenceList: ReferenceItem[] = []
    parse(htmlContent as string, {
      replace: (dom) => {
        if (dom.type === 'tag') {
          if (dom.attribs && dom.attribs.id === 'react-component-refs') {
            const refsPage = dom.attribs['data-page-refs']
            if (refsPage) {
              const refsList = JSON.parse(decodeURIComponent(refsPage)).map(
                (ref: { ref: { siteUrl: string; url: string }; type: string }) => ({
                  ...ref.ref,
                  type: ref.type,
                  url: ref.type === 'MEDIA' ? ref.ref.siteUrl : ref.ref.url,
                }),
              )
              tempReferenceList.push(...refsList)
            }
          }
        }
      },
    })
    setReferenceList(tempReferenceList)
  }, [htmlContent])

  if (typeof content !== 'string') {
    return null
  }

  return (
    <div className='ai-smarties-md-text'>
      {parse(htmlContent as string, {
        replace: (dom) => {
          if (dom.type === 'tag') {
            if (dom.attribs && dom.attribs.class === 'ai-smarties-md-trigger') {
              const index = Number(dom.attribs['data-index']) - 1
              const currentList = list.length ? list : referenceList
              if (!isNaN(index) && index < currentList.length) {
                return <Trigger index={index} url={currentList[index].url} />
              }
            }
            if (dom.attribs && dom.attribs.id === 'react-component-refs') {
              return null
            }
            if (dom.attribs && dom.attribs.class === 'ai-smarties-md-item') {
              const pdfPage = dom.attribs['data-pdf-page']
              return (
                <span
                  className='cursor-pointer text-primary'
                  onClick={() => {
                    onClickPageNumber?.({ pageNumber: Number(pdfPage) })
                  }}>
                  [{pdfPage}]
                </span>
              )
            }
            if (dom.attribs && dom.attribs.class === 'ai-smarties-sref-trigger') {
              const srefTag = decodeURIComponent(dom.attribs['data-sref'] || '')
              return <SrefTrigger srefTag={srefTag} />
            }
            return undefined
          }
        },
      })}
      {referenceList.length > 0 && (
        <ReferenceList referenceList={referenceList} className='!text-base !font-bold' />
      )}
    </div>
  )
}

export default MarkdownParser
