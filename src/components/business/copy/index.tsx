import { MessageRoleEnum } from '@/api/getSession'
import { deleteReportSource } from '@/api/report/deleteSource'
import {
  saveChatSource,
  saveMediaSource,
  savePdfSource,
  SourceTypeEnum,
} from '@/api/report/saveSource'
import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { MessageType } from '@/types'
import { BookmarkIcon, CheckIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline'
import { BookmarkIcon as SavedIcon } from '@heroicons/react/24/solid'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'

export interface CopyProps {
  question: string | MessageType
  sourceFromId?: string
  sourceType: SourceTypeEnum
  content: string
  sourceId?: string
  procId: string
  saveToReport?: boolean
  source?: string
}

export const Copy: React.FC<CopyProps> = ({
  sourceFromId,
  sourceType,
  content,
  sourceId,
  procId,
  question,
  saveToReport = true,
  source,
}) => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const [copied, setCopied] = useState(false)
  const [reportSourceId, setReportSourceId] = useState(sourceId)

  const handleClickCopy = async () => {
    try {
      setCopied(true)
      setTimeout(() => {
        setCopied(false)
      }, 3000)
      const clipboardItem = new ClipboardItem({
        'text/plain': new Blob([content], { type: 'text/plain' }),
      })
      await navigator.clipboard.write([clipboardItem])
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  const handleClickSave = async () => {
    if (!reportSourceId) {
      let newSourceId
      if (sourceType === SourceTypeEnum.CHAT) {
        newSourceId = (
          await saveChatSource(
            [
              {
                content: question as string,
                role: MessageRoleEnum.USER,
                processId: procId,
              },
              {
                content,
                role: MessageRoleEnum.ASSISTANT,
                processId: procId,
              },
            ],
            sourceFromId!,
            true,
          )
        ).id
      } else if (sourceType === SourceTypeEnum.MEDIA) {
        newSourceId = (
          await saveMediaSource(
            [
              {
                content: question as string,
                role: MessageRoleEnum.USER,
                mediaProcessId: procId,
              },
              {
                content,
                role: MessageRoleEnum.ASSISTANT,
                mediaProcessId: procId,
              },
            ],
            sourceFromId!,
            true,
            source,
          )
        ).id
      } else if (sourceType === SourceTypeEnum.PDF) {
        newSourceId = (
          await savePdfSource(
            [
              {
                content: question as string,
                role: MessageRoleEnum.USER,
                pdfProcessId: procId,
              },
              {
                content,
                role: MessageRoleEnum.ASSISTANT,
                pdfProcessId: procId,
              },
            ],
            sourceFromId!,
            true,
            source,
          )
        ).id
      }
      toast.success(t('toast.success'), {
        duration: 3000,
      })
      setReportSourceId(newSourceId)
    }
  }

  const handleClickCancelSave = async () => {
    if (reportSourceId) {
      setReportSourceId(undefined)
      await deleteReportSource(reportSourceId)
    }
  }

  return (
    <>
      <div className='flex h-8 w-full flex-row items-center justify-between'>
        {copied ? (
          <div className='flex-center gap-1'>
            <CheckIcon className='h-4 w-4 text-success' />
            <Text type={TextEnum.Body_small} className='text-success'>
              {t('button.copy')}
            </Text>
          </div>
        ) : (
          <DocumentDuplicateIcon
            className='h-4 w-4 cursor-pointer'
            color='text-secondary-black-2'
            onClick={handleClickCopy}
          />
        )}
        {saveToReport ? (
          reportSourceId ? (
            <div
              className='flex cursor-pointer gap-1 text-secondary-black-2'
              onClick={handleClickCancelSave}>
              <SavedIcon className='h-4 w-4' color='blue' />
              <Text type={TextEnum.Body_small}>{t('button.cancelSave')}</Text>
            </div>
          ) : (
            <div
              className='flex cursor-pointer gap-1 text-secondary-black-2'
              onClick={handleClickSave}>
              <BookmarkIcon className='h-4 w-4' />
              <Text type={TextEnum.Body_small}>{t('button.save')}</Text>
            </div>
          )
        ) : null}
      </div>
    </>
  )
}
