import React from 'react'
import clsx from 'clsx'
import style from './index.module.scss'

export enum TextEnum {
  H1,
  H2,
  H3,
  H4,
  H5,
  H6,
  H7,
  Body_big,
  Body_medium,
  Body_small,
  Title_big,
}

export interface TextProps {
  children?: string | React.ReactNode
  onClick?: () => void
  type?: TextEnum
  className?: string | string[]
}

type TagMappingType = {
  [value in TextEnum]: string
}

type ClassNameType = {
  [value in TextEnum]: string
}

const typeMapping: TagMappingType = {
  [TextEnum.H1]: 'h1',
  [TextEnum.H2]: 'h2',
  [TextEnum.H3]: 'h3',
  [TextEnum.H4]: 'h4',
  [TextEnum.H5]: 'h5',
  [TextEnum.H6]: 'h6',
  [TextEnum.H7]: 'h6',
  [TextEnum.Title_big]: 'div',
  [TextEnum.Body_big]: 'div',
  [TextEnum.Body_medium]: 'div',
  [TextEnum.Body_small]: 'div',
}

const classNameMapping: ClassNameType = {
  [TextEnum.H1]: style.h1,
  [TextEnum.H2]: style.h2,
  [TextEnum.H3]: style.h3,
  [TextEnum.H4]: style.h4,
  [TextEnum.H5]: style.h5,
  [TextEnum.H6]: style.h6,
  [TextEnum.H7]: style.h7,
  [TextEnum.Title_big]: style.title_big,
  [TextEnum.Body_big]: style.body_big,
  [TextEnum.Body_medium]: style.body_medium,
  [TextEnum.Body_small]: style.body_small,
}

export const Text: React.FC<TextProps> = ({
  type = TextEnum.H1,
  className,
  ...rest
}: TextProps) => {
  const tagType = typeMapping[type]
  const classes = clsx(classNameMapping[type], className)

  return React.createElement(tagType, { className: classes, ...rest })
}
