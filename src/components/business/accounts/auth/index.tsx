import { useRef } from 'react'
import Image from 'next/image'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'

interface LoginOptions {
  type: 'google' | 'linkedin'
}

interface IAuthLoginProps {
  onAuthLogin?: (options: LoginOptions) => void
}

export const AuthLogin = (props: IAuthLoginProps) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.LOGIN])
  const googleButtonRef = useRef<HTMLButtonElement>(null)

  return (
    <div className='flex-center w-full gap-5' data-component='AuthLogin'>
      <Button
        onClick={() => {
          props.onAuthLogin?.({
            type: 'google',
          })
        }}
        ref={googleButtonRef}
        className='h-12 flex-auto cursor-pointer rounded-sm border-2 border-[#E4E5Ec] bg-transparent text-base hover:bg-transparent'>
        <Image src='/images/google.png' alt='google' className='mr-2' width={16} height={16} />
        {t('login:auth.google')}
      </Button>

      <Button
        onClick={() => {
          props.onAuthLogin?.({
            type: 'linkedin',
          })
        }}
        className='h-12 flex-auto cursor-pointer rounded-sm border-2 border-[#E4E5Ec] bg-transparent text-base hover:bg-transparent'>
        <Image src='/images/linkedin.png' alt='linkedin' className='mr-2' width={20} height={20} />
        {t('login:auth.linkedin')}
      </Button>
    </div>
  )
}
