import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export interface DeleteMenuModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
}
const DeleteMenuModal: React.FC<DeleteMenuModalProps> = ({
  open,
  onClose,
  onConfirm,
}: DeleteMenuModalProps) => {
  const { t } = useTranslation(Namespace.GLOBAL)

  const [disabled, setDisabled] = useState(false)

  const handleCloseModal = () => {
    if (onClose) onClose()
  }

  const handleClickConfirm = () => {
    setDisabled(true)
    if (onConfirm) {
      onConfirm()
      setDisabled(false)
    }
  }

  return (
    <>
      <Modal open={open} className='relative min-w-[420px] p-6'>
        <div className='absolute right-2.5 top-2.5'>
          <XMarkIcon className='h-5 w-5 cursor-pointer' onClick={onClose} />
        </div>
        <div>
          <Text type={TextEnum.H4}> {t('global:tip')}</Text>
          <Text type={TextEnum.Body_big} className='mt-4'>
            {t('global:modal.deleteContent')}
          </Text>
        </div>
        <div className='mt-5 flex w-full justify-end gap-2'>
          <Button variant='secondary' onClick={handleCloseModal}>
            {t('global:button.cancel')}
          </Button>
          <Button onClick={handleClickConfirm} disabled={disabled}>
            {t('global:button.confirm')}
          </Button>
        </div>
      </Modal>
    </>
  )
}

export default DeleteMenuModal
