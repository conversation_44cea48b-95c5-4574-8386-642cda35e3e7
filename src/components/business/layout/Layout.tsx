import { useEffect, useRef } from 'react'
import clsx from 'clsx'
import { TopBarHeight } from './TopBar'
import SideBar from './SideBar'
import { PageHeader } from '../page-header'
import { MenuComponentHandles } from './Menu'
import React from 'react'
import { TabName } from '../page-header/TabsComponent'
interface LayoutBaseType {
  children: string | React.ReactNode
  sideBarContent?: string | React.ReactNode
  sideBarRightContent?: string | React.ReactNode
  className?: string
  showSideBar?: boolean
  showRightSideBar?: boolean
  tabName?: TabName
}

interface LayoutProps extends LayoutBaseType {}

/**
 * @param {JSX.element} children Children component to be wrapped around the Layout component.
 * @return The layout of the page which contains the topbar, page header and children components.
 */
const Layout = React.forwardRef<MenuComponentHandles, LayoutProps>((props, ref) => {
  const {
    children,
    className,
    sideBarContent,
    showSideBar = true,
    showRightSideBar = false,
    tabName,
    sideBarRightContent,
  } = props
  const sidebarRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const handleScroll = () => {
    const position = window.scrollY
    if (sidebarRef.current && contentRef.current) {
      if (contentRef.current.offsetHeight > window.innerHeight) {
        sidebarRef.current.style.height = `${
          window.innerHeight - (position >= TopBarHeight ? 0 : TopBarHeight - position)
        }px`
      }
    }
  }

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
    }
  }, [])

  return (
    <div
      className={clsx(
        'flex h-screen min-h-full w-full max-w-full flex-col bg-background',
        className,
      )}>
      <PageHeader tabName={tabName} />
      <div className='flex h-screen-minus-header flex-row' ref={contentRef}>
        {showSideBar && (
          <SideBar sidebarRef={sidebarRef} ref={ref}>
            {sideBarContent}
          </SideBar>
        )}
        <div className='flex-1'>{children}</div>
        {showRightSideBar && (
          <SideBar sidebarRef={sidebarRef} left ref={ref}>
            {sideBarRightContent}
          </SideBar>
        )}
      </div>
    </div>
  )
})

Layout.displayName = 'Layout'

export default Layout
