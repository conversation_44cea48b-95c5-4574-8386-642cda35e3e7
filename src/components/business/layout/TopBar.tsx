import React from 'react'
import Image from 'next/image'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import clsx from 'clsx'
import router from 'next/router'
import { smartiesRoutes } from '@/routers'

export const TopBar = ({ className }: { className?: string | string[] }) => {
  const { t } = useTranslation(Namespace.GLOBAL)

  const handleClickLogo = () => {
    if (router.pathname !== smartiesRoutes.home) {
      router.push(smartiesRoutes.home)
    }
  }

  return (
    <div
      className={clsx(
        'flex-center h-16 min-w-64 cursor-pointer overflow-hidden border-b',
        className,
      )}
      onClick={handleClickLogo}>
      <Image className={clsx('mr-3')} src='/images/logo.svg' alt='Logo' width={40} height={40} />
      <p className='min-w-[90px] text-base font-bold'> {t('name')} </p>
    </div>
  )
}

export const TopBarHeight = 64
