import { FilmIcon } from '@heroicons/react/24/outline'
import { Text, TextEnum } from '../../text'
import Image from 'next/image'
import { useCallback, useState } from 'react'
import ImgPreview from './ImgPreview'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { MediaItemType } from '@/api/getMedia'
import { getUserResponse } from '@/api/getUser'

export interface ImgSectionProps {
  imgs?: Array<MediaItemType>
  onCloseModal: () => Promise<getUserResponse>
}

const ImgSection: React.FC<ImgSectionProps> = ({ imgs, onCloseModal }: ImgSectionProps) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)

  const firstThreeImages = imgs ? imgs.slice(0, 3) : []
  const moreImages = imgs ? imgs.slice(3, 6) : []

  const [open, setOpen] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)

  const handleOpenPreview = useCallback((index: number) => {
    setOpen(true)
    setCurrentIndex(index)
  }, [])

  return (
    <>
      {imgs === undefined || imgs.length === 0 ? null : (
        <>
          <div className='mt-2 grid cursor-pointer grid-cols-2 gap-2'>
            {firstThreeImages.map((item, index) => (
              <div
                key={index}
                className='relative h-[104px] w-[156px] overflow-hidden rounded-sm border border-border bg-gray-imgBg p-0.5'>
                <Image
                  src={item?.s3Url ?? item.mediaUrl}
                  alt={`image-${index}`}
                  fill
                  sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 156px'
                  className='rounded-sm'
                  onClick={() => {
                    handleOpenPreview(index)
                  }}
                />
              </div>
            ))}
            {imgs.length > 3 && (
              <div className='h-[104px] w-[156px] rounded-sm border p-2'>
                <div className='mb-1 flex flex-row gap-1'>
                  {moreImages.map((item, index) => (
                    <div
                      className='relative h-11 w-11 rounded-sm border border-border bg-gray-imgBg'
                      key={index}>
                      <Image
                        src={item?.s3Url ?? item.mediaUrl}
                        alt={`image-${index + 3}`}
                        fill
                        sizes='44px' // 缩略图大小
                        className='rounded-sm'
                        onClick={() => {
                          handleOpenPreview(index + 3)
                        }}
                      />
                    </div>
                  ))}
                </div>
                <div
                  onClick={() => {
                    handleOpenPreview(0)
                  }}
                  className='flex-center h-[40px] w-[140px] rounded-sm border-2 border-dashed border-gray-300'>
                  <FilmIcon className='mr-2 h-4 w-4' />
                  <Text type={TextEnum.Body_medium}>{t('chat.rightSide.viewMore')}</Text>
                </div>
              </div>
            )}
          </div>
          <ImgPreview
            open={open}
            onClose={() => {
              setOpen(false)
              if (onCloseModal) onCloseModal()
            }}
            imgs={imgs}
            clickedIndex={currentIndex}
          />
        </>
      )}
      {imgs?.map((item, index) => (
        <div className='relative hidden h-[615px] w-[896px] bg-card' key={index}>
          <Image
            src={item?.s3Url ?? item.mediaUrl}
            alt={`image-${index}`}
            fill
            sizes='896px'
            className='rounded-sm'
            style={{
              objectFit: 'scale-down',
            }}
            priority
            quality={100}
          />
        </div>
      ))}
    </>
  )
}

export default ImgSection
