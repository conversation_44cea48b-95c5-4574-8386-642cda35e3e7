import { MediaItemType } from '@/api/getMedia'
import { getUserResponse } from '@/api/getUser'
import { PDFItemType } from '@/api/pdfs/getPdf'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { Text, TextEnum } from '../text'
import ImgSection from './img/ImgSection'
import PDFSection from './pdf/PDFSection'
import RecommandSkeletonItem from './skeleton/Skeleton'

export type AssetsType = 'pdf' | 'img'
type FormPageType = 'search' | 'document'
export interface RecommendProps {
  searchingPdf: boolean // 显示骨架屏
  searchingImg: boolean // 显示骨架屏
  pdfs?: PDFItemType[]
  imgs?: Array<MediaItemType>
  onCloseModal: () => Promise<getUserResponse>
  processId: string
  type?: FormPageType
}

export const Recommend = ({
  searchingPdf,
  searchingImg,
  pdfs,
  imgs,
  onCloseModal,
  processId,
  type = 'search',
}: RecommendProps) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)

  if (searchingPdf || searchingImg) {
    return <RecommandSkeletonItem count={4} className='mt-3' />
  }

  return (
    <>
      {pdfs.length || imgs.length ? (
        <>
          <Text type={TextEnum.H5} className='mt-2 text-secondary-black-1'>
            {t(`chat.rightSide.${type === 'search' ? 'recommend' : 'saveDocument'}`)}
          </Text>
          <Text type={TextEnum.Body_small} className='mb-4 text-secondary-black-3'>
            {t('chat.rightSide.recommendTip')}
          </Text>
        </>
      ) : null}
      <PDFSection pdfs={pdfs} processId={processId} />
      <ImgSection imgs={imgs} onCloseModal={onCloseModal} />
    </>
  )
}
