import clsx from 'clsx'
import styles from './skeleton.module.scss'
import { useTheme } from '@/context/ThemeContext'

export interface SkeletonProps {
  count?: number
  className?: string
}

const RecommandSkeletonItem: React.FC<SkeletonProps> = ({
  count = 1,
  className,
}: SkeletonProps) => {
  const array = new Array(count).fill('')
  const { theme } = useTheme()

  return (
    <div className={clsx('flex w-full flex-col gap-2', className)}>
      {array.map((item, index) => (
        <div
          key={index}
          className={clsx(
            'h-[52px] w-full rounded-md',
            theme === 'light' ? styles.gradient : styles.darkGradient,
          )}
        />
      ))}
    </div>
  )
}

export default RecommandSkeletonItem
