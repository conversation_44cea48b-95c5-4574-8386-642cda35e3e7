import React, { useState } from 'react'
import Image from 'next/image'
import ContactUsModal from '../contact-us/ContactUsModal'

export const HelpCenter = () => {
  const [openContactModal, setOpenContactModal] = useState(false)

  const handleClick = () => {
    setOpenContactModal(true)
  }

  return (
    <>
      <div className='fixed bottom-10 right-10 h-12 w-12 cursor-pointer' onClick={handleClick}>
        <Image src='/images/help.svg' alt='no data' width={48} height={48} />
      </div>
      <ContactUsModal
        open={openContactModal}
        onClose={() => {
          setOpenContactModal(false)
        }}
      />
    </>
  )
}
