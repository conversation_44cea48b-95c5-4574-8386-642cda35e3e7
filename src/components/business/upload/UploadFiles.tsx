import { TextEnum, Text } from '@/components/business/text'
import { But<PERSON> } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { FileUp, Loader } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { Progress } from '@/components/ui/progress'
import clsx from 'clsx'
import Image from 'next/image'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { getSignedUrl } from '@/api/pdfs/getSignedUrl'
import { cloneDeep } from 'lodash'
import axios from 'axios'
import { toast } from 'sonner'
export const ALLOWED_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp',
]

export interface UploadFilesProps {
  open: boolean
  onNext: () => void
  onCloseModal: () => void
  onUploadFiles: (urlList: string[]) => Promise<void>
  acceptType: string
  isShowUrl: boolean
}

export interface FileItem {
  file: File
  progress: number
  url: string | null
  s3key: string | null
  fail: boolean
  isUploading: boolean
  isCapacityExceeded?: boolean
}

const MAX_FILES = 20
const MAX_SIZE = '50M'
const MAX_SIZE_NUM = 50

export const UploadFiles: React.FC<UploadFilesProps> = ({
  open,
  onNext,
  onCloseModal,
  onUploadFiles,
  acceptType = 'application/pdf,image/*',
  isShowUrl,
  isCapacityExceeded,
}) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.DEEPEXPLORE])

  const [disableImportBtn, setDisableImportBtn] = useState<boolean>(true)
  const [importing, setImporting] = useState<boolean>(false)
  const [files, setFiles] = useState<FileItem[]>([])
  const [isUploading, setIsUploading] = useState(false) // Track if any file is uploading
  const [upToMaxNum, setUpToMaxNum] = useState(false)
  const [errorFileTypeFile, setErrorFileTypeFile] = useState<string>('')
  const [upToMaxSizeFile, setUpToMaxSizeFile] = useState<string>('')

  // reset data
  useEffect(() => {
    setDisableImportBtn(true)
  }, [open])

  useEffect(() => {
    console.log('最新 files', files)
    // reset
    if (files.length === 0) {
      setErrorFileTypeFile('')
      setUpToMaxSizeFile('')
      setUpToMaxNum(false)
    }
    // start upload
    files.forEach((file, index) => {
      if (!file.url && !file.fail && !file.isUploading) {
        file.isUploading = true
        uploadFile({
          fileObj: file.file,
          index,
        })
      }
    })
    // check if exist uploading
    const finishedUploading = files.every((file) => file.progress === 100 || file.fail)
    setIsUploading(!finishedUploading)

    setDisableImportBtn(
      files.length < 1 || !files.every((file) => file.progress === 100 || file.fail),
    )
  }, [files])

  const uploadFile = async ({ fileObj, index }: { fileObj: File; index: number }) => {
    try {
      setIsUploading(true)

      // Step 1: Get signed URL from backend
      const resp = await getSignedUrl({
        fileName: fileObj.name,
      })

      if (resp.url) {
        const host = resp.url
        const fullUrl = resp.url + resp.fields['key']

        setFiles((prev) =>
          prev.map((f, i) => (i === index ? { ...f, url: fullUrl, s3key: resp.fields['key'] } : f)),
        )

        const formData = new FormData()

        formData.append('key', resp.fields['key'])
        formData.append('ACL', resp.fields['ACL'])
        formData.append('Content-Type', resp.fields['Content-Type'])
        formData.append('x-amz-meta-userId', resp.fields['x-amz-meta-userId'])
        formData.append('bucket', resp.fields['bucket'])
        formData.append('X-Amz-Algorithm', resp.fields['X-Amz-Algorithm'])
        formData.append('X-Amz-Credential', resp.fields['X-Amz-Credential'])
        formData.append('X-Amz-Date', resp.fields['X-Amz-Date'])
        formData.append('X-Amz-Security-Token', resp.fields['X-Amz-Security-Token'])
        formData.append('Policy', resp.fields['Policy'])
        formData.append('X-Amz-Signature', resp.fields['X-Amz-Signature'])

        formData.append('file', fileObj)

        try {
          const response = await axios.post(host, formData, {
            headers: {},
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
                // 更新进度到文件列表
                setFiles((prev) =>
                  prev.map((f, i) =>
                    // 不要更新到 100% 要等S3返回才是真正的100%
                    i === index ? { ...f, progress: Math.min(progress, 90), fail: false } : f,
                  ),
                )
              }
            },
          })

          if (response.status === 204) {
            setFiles((prev) =>
              prev.map((f, i) =>
                i === index ? { ...f, url: fullUrl, progress: 100, fail: false } : f,
              ),
            )
          } else {
            setFiles((prev) =>
              prev.map((f, i) => (i === index ? { ...f, url: fullUrl, fail: true } : f)),
            )
          }
        } catch (error) {
          setFiles((prev) =>
            prev.map((f, i) => (i === index ? { ...f, url: fullUrl, progress: 0, fail: true } : f)),
          )
        }
      }
    } catch (error) {
      console.error('Upload error:', error)
      // Reset progress in case of error
      setFiles((prev) => prev.map((f, i) => (i === index ? { ...f, progress: 0, fail: true } : f)))
    } finally {
      setIsUploading(!files.every((f) => f.progress === 100 || f.fail))
    }
  }
  // 允许的文件类型

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    console.log('files = ', e.target.files, selectedFiles)

    // 重置文件输入框，使其可以重新触发 onChange
    e.target.value = ''

    // check files
    const validFiles = selectedFiles.filter((file) => {
      if (!ALLOWED_TYPES.includes(file.type)) {
        setErrorFileTypeFile(file.name)
        return false
      }
      if (file.size > MAX_SIZE_NUM * 1024 * 1024) {
        setUpToMaxSizeFile(file.name)
        return false
      }
      return true
    })
    // 存在不满足要求的文件，终止
    if (validFiles.length < selectedFiles.length) {
      return
    } else {
      setErrorFileTypeFile('')
      setUpToMaxSizeFile('')
    }
    // check, 每次只取五个文件
    // 先计算可取的文件个数
    const availableFiles = MAX_FILES - files.length
    if (isCapacityExceeded) {
      return toast(
        <Trans
          t={t}
          i18nKey='deepExplore:importFile.upload.uploadLimitError'
          values={{ count: MAX_FILES }}
        />,
        {
          duration: 3000,
        },
      )
    }
    if (files.length + selectedFiles.length > MAX_FILES) {
      // setUpToMaxNum(true)
      toast(
        <Trans
          t={t}
          i18nKey='deepExplore:importFile.upload.fileNumError'
          values={{ count: MAX_FILES }}
        />,
        {
          duration: 3000,
        },
      )
      // return
    } else {
      // setUpToMaxNum(false)
    }

    // Map selected files with initial progress and add to state
    const newFiles = validFiles.slice(0, availableFiles).map((file) => ({
      file,
      progress: 0,
      url: null,
      fail: false,
      s3key: null,
      isUploading: false,
    }))

    setFiles((prev) => [...prev, ...newFiles])
  }

  const handleConfirmImport = async () => {
    const uploadedUrls = files.map((f) => {
      return {
        name: f.file.name,
        url: f.url,
        s3Key: f.s3key,
        size: f.file.size,
      }
    })
    if (onUploadFiles) {
      setImporting(true)
      setDisableImportBtn(true)
      await onUploadFiles(uploadedUrls)
      setImporting(false)
      setDisableImportBtn(false)
    }
  }

  const handleRemoveFile = (index: number) => {
    const newFiles = cloneDeep(files)
    newFiles.splice(index, 1)
    setFiles(newFiles)
    setIsUploading(!newFiles.every((f) => f.progress === 100 || f.fail))
  }

  return (
    <>
      {!open ? null : (
        <>
          <Text type={TextEnum.H4}>{t('deepExplore:importFile.title')}</Text>
          <div className='flex-center mt-4 max-w-[500px] flex-col rounded-md border border-dashed border-border !bg-modal-in-card p-6'>
            <FileUp className='mb-4 h-12 w-12 text-primary' />
            <div className='flex'>
              <Text
                type={TextEnum.Body_medium}
                className={clsx(
                  'inline cursor-pointer text-primary underline',
                  files.length >= MAX_FILES ? 'text-primary-disabled' : '',
                )}>
                <label className='cursor-pointer'>
                  <input
                    type='file'
                    multiple
                    accept={acceptType}
                    className='hidden'
                    onChange={handleFileChange}
                    disabled={files.length >= MAX_FILES}
                    max={5}
                  />
                  {t('deepExplore:importFile.upload.upload')}
                </label>
              </Text>
              {isShowUrl && (
                <Text type={TextEnum.Body_medium} className='ml-1 !text-foreground'>
                  <Trans
                    i18nKey='deepExplore:importFile.upload.paste'
                    t={t}
                    components={[
                      <></>,
                      <Text
                        onClick={() => {
                          if (files.length > 0 || isUploading) return
                          onNext()
                        }}
                        type={TextEnum.Body_medium}
                        className={clsx(
                          'inline text-primary underline',
                          isUploading || files.length > 0
                            ? 'text-secondary-black-3'
                            : 'cursor-pointer',
                        )}
                        key={1}
                      />,
                    ]}
                  />
                </Text>
              )}
            </div>
            <Text type={TextEnum.Body_small} className='mt-2.5 text-secondary-black-3'>
              <Trans
                t={t}
                i18nKey={`deepExplore:importFile.upload.${acceptType.includes('image') ? 'limitOrImg' : 'limit'}`}
                values={{ count: MAX_FILES, size: MAX_SIZE }}
              />
            </Text>
            {upToMaxNum && (
              <Text type={TextEnum.Body_small} className='mt-2.5 text-destructive'>
                <Trans
                  t={t}
                  i18nKey='deepExplore:importFile.upload.fileNumError'
                  values={{ count: MAX_FILES }}
                />
              </Text>
            )}
            {errorFileTypeFile && (
              <Text type={TextEnum.Body_small} className='mt-2.5 text-destructive'>
                <Trans
                  t={t}
                  i18nKey={`deepExplore:importFile.upload.${acceptType.includes('image') ? 'fileTypeErrorOrImg' : 'fileTypeError'}`}
                  values={{ name: errorFileTypeFile }}
                />
              </Text>
            )}
            {upToMaxSizeFile && (
              <Text type={TextEnum.Body_small} className='mt-2.5 text-destructive'>
                <Trans
                  t={t}
                  i18nKey='deepExplore:importFile.upload.fileSizeError'
                  values={{ name: upToMaxSizeFile }}
                />
              </Text>
            )}
          </div>

          <div
            className={clsx(
              'mt-2 flex max-h-[calc(100vh-92px-330px)] max-w-[500px] flex-col gap-2 overflow-auto',
            )}>
            {files.map((fileObj, index) => (
              <div key={index} className='flex flex-col rounded-sm bg-secondary p-2'>
                {/* line 1 */}
                <div className='flex-v-center'>
                  <Image
                    src={'/images/pdf_icon.svg'}
                    alt=''
                    width={32}
                    height={32}
                    className='mr-3 shrink-0'
                  />
                  <Text
                    type={TextEnum.H6}
                    className='flex-1 overflow-hidden text-ellipsis whitespace-nowrap'>
                    {fileObj.file.name}
                  </Text>
                  <XMarkIcon
                    className='ml-1.5 h-6 w-6 shrink-0 cursor-pointer'
                    onClick={() => {
                      handleRemoveFile(index)
                    }}
                  />
                </div>
                {/* line 2 */}
                <div className='flex-v-center mt-1'>
                  {fileObj.fail ? (
                    <Text
                      type={TextEnum.Body_small}
                      className='ml-11 w-9 flex-1 flex-grow text-destructive'>
                      {t('deepExplore:importFile.upload.uploadFailed')}
                    </Text>
                  ) : (
                    <>
                      <Progress value={fileObj.progress} className='h-1.5 bg-card' />

                      <Text type={TextEnum.Body_small} className='ml-1 w-9'>
                        {fileObj.progress}%
                      </Text>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className='mt-5 flex h-10 items-center justify-end'>
            <Button onClick={onCloseModal} variant='outline' className='border-0'>
              {t('button.cancel')}
            </Button>
            <Button
              asm-tracking='TEST_UPLOAD_FINISH_FILE:CLICK'
              onClick={handleConfirmImport}
              className='flex-center ml-2'
              disabled={disableImportBtn}>
              {importing ? (
                <>
                  <Loader className='mr-1 h-4 w-4 animate-spin360' />
                  {t('button.import')}
                </>
              ) : (
                t('button.import')
              )}
            </Button>
          </div>
        </>
      )}
    </>
  )
}
