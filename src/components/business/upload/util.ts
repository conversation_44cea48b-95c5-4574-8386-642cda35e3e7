const fileTypeMap = {
  pdf: 'pdf',
  jpg: 'jpg',
  jpeg: 'jpg',
  png: 'jpg',
  gif: 'jpg',
  bmp: 'jpg',
  webp: 'jpg',
}
// 文件格式过滤
export const filterFileType = (file) => {
  let str = ''
  if (file.folder) {
    str = 'folder'
  } else if (file.name || file.filename) {
    const name = file.name || file.filename
    const index = name.lastIndexOf('.')
    const key = name.substring(index + 1, file.length).toLowerCase()
    str = fileTypeMap[key] || 'unknow'
  }
  return str
}
