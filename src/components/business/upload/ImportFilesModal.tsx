import { Modal } from '@/components/business/modal'
import { useEffect, useState } from 'react'
import { PasteUrl } from '../../../pages/deep-explore/components/PasteUrl'
import { UploadFiles } from './UploadFiles'

export interface ImportFilesModalProps {
  open: boolean
  onClose: () => void
  onConfirmPaste?: (url: string) => Promise<void>
  onUploadFiles: (urlList: string[]) => Promise<void>
  isShowUrl: boolean
  isCapacityExceeded?: boolean
}

export const ImportFilesModal: React.FC<ImportFilesModalProps> = ({
  open,
  onClose,
  onConfirmPaste,
  onUploadFiles,
  isShowUrl = true,
  isCapacityExceeded,
}) => {
  const [step, setStep] = useState<'uploadFiles' | 'pasteUrl'>('uploadFiles')

  // reset data
  useEffect(() => {
    setStep('uploadFiles')
  }, [open])

  // 关闭弹窗
  const handleCancel = () => {
    if (onClose) onClose()
  }

  return (
    <>
      <Modal
        showCloseIcon
        open={open}
        className='max-h-[calc(100vh-92px)] min-h-[330px] w-[434px] break-all p-6'
        overlayClassName='overflow-auto'
        onClose={handleCancel}
        overlayClose={false}>
        {isShowUrl && (
          <PasteUrl
            open={step === 'pasteUrl'}
            onBack={() => {
              setStep('uploadFiles')
            }}
            onConfirmPaste={onConfirmPaste}
          />
        )}

        <UploadFiles
          open={step === 'uploadFiles'}
          onNext={() => {
            setStep('pasteUrl')
          }}
          isCapacityExceeded={isCapacityExceeded}
          onCloseModal={handleCancel}
          onUploadFiles={onUploadFiles}
          isShowUrl={isShowUrl}
        />
      </Modal>
    </>
  )
}
