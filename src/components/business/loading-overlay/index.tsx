import React, { useEffect } from 'react'
import { isServer, withPortal } from '../hoc/WithPortal'

export interface LoadingOverlayProps {
  open?: boolean
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = withPortal(
  ({ open }: LoadingOverlayProps) => {
    useEffect(() => {
      if (!open || isServer()) return

      document.body.style.overflow = 'hidden'

      return () => {
        document.body.style.overflow = 'auto'
      }
    }, [open])

    if (!open) {
      return <></>
    }

    return (
      <>
        <div className={'flex-center fixed bottom-0 left-0 right-0 top-0 bg-black-1-70'}>
          <img src='/images/loading.svg' className='mr-1 h-9 w-9 animate-spin-slow' />
        </div>
      </>
    )
  },
)

export default LoadingOverlay
