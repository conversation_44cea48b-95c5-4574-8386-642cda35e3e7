import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
// import { Input } from '@/components/ui/input'
import { sharePdfLink, shareReportLink, shareSessionLink } from '@/api/shareLink'
import { Text, TextEnum } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/useToast'
import { Namespace } from '@/i18n'
import { Tracking, TrackingEventType } from '@/lib/tracking'
import { ShareTypeEnum } from '@/types'
import { CheckIcon, LockClosedIcon, LockOpenIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

const ShareLink = ({
  shareType,
  shareData,
  buttonType,
}: {
  shareType: ShareTypeEnum
  shareData?: { title?: string; pdfId?: string; pdfName?: string }
  buttonType?: 'default' | 'outline'
}) => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const [isSecret, setIsSecret] = useState(false)
  const [copyText, setCopyText] = useState('')
  const [linkCopied, setLinkCopied] = useState<boolean>(false)
  const toast = useToast()
  const router = useRouter()
  const { reportId, sessionId } = router.query
  // const [indexing, setIndexing] = useState(true)

  const handleAccess = async (isSecret: boolean) => {
    try {
      if (shareType === ShareTypeEnum.REPORT) {
        await shareReportLink({
          reportId: reportId as string,
          action: isSecret ? 'unshare' : 'share',
        })
      } else if (shareType === ShareTypeEnum.PDF) {
        await sharePdfLink({
          pdfId: shareData?.pdfId || '',
          action: isSecret ? 'unshare' : 'share',
        })
      } else {
        await shareSessionLink({
          sessionId: sessionId as string,
          action: isSecret ? 'unshare' : 'share',
        })
      }
    } catch (e) {
      console.error(e)
    }
  }

  const getCopyText = (type: ShareTypeEnum) => {
    const baseUrl = process.env.NEXT_PUBLIC_SHARE_LINK

    switch (type) {
      case ShareTypeEnum.GENERAL:
        return `${baseUrl}/share/general/${sessionId}`
      case ShareTypeEnum.MARKET:
        return `${baseUrl}/share/market/${sessionId}`
      case ShareTypeEnum.COMPANY:
        return `${baseUrl}/share/company/${sessionId}`
      case ShareTypeEnum.TOPIC:
        return `${baseUrl}/share/topic/${sessionId}`
      case ShareTypeEnum.REGULATION:
        return `${baseUrl}/share/regulation/${sessionId}`
      case ShareTypeEnum.RISK:
        return `${baseUrl}/share/risk/${sessionId}`
      case ShareTypeEnum.SWOT:
        return `${baseUrl}/share/swot/${sessionId}`
      case ShareTypeEnum.REPORT:
        return `${baseUrl}/share/personal-report/${reportId}?title=${shareData?.title}`
      case ShareTypeEnum.PDF:
        return `${baseUrl}/share/aipdf/${shareData?.pdfName || ''}?pdfId=${shareData?.pdfId}`
      default:
        return ''
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(encodeURI(getCopyText(shareType)))
      setLinkCopied(true)
      toast.success(t('shareLink.copySuccess'))
    } catch (e) {
      console.error(e)
    }
  }

  const handleShare = async () => {
    await handleAccess(false)
    setIsSecret(false)
    const getMixpanelKey = () => {
      switch (shareType) {
        case ShareTypeEnum.REPORT:
          return 'SHARE_REPORT'
        case ShareTypeEnum.PDF:
          return 'SHARE_PDF'
        default:
          return 'SHARE_SEARCH'
      }
    }
    Tracking.trackEvent(getMixpanelKey(), TrackingEventType.CLICK, {
      value: copyText,
    })
  }

  useEffect(() => {
    setCopyText(getCopyText(shareType))
  }, [shareType, reportId, sessionId, shareData])

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            className='h-8 border-none px-3 py-0'
            variant={buttonType === 'default' ? 'default' : 'outline'}
            onClick={() => {
              handleShare()
              setLinkCopied(false)
            }}>
            <Text type={TextEnum.Body_medium} className='flex-v-center'>
              {t('shareLink.share')}
            </Text>
          </Button>
        </PopoverTrigger>
        <PopoverContent side='bottom' align='start' className='p-0'>
          <div>
            <div className='p-4'>
              <Text type={TextEnum.Body_small} className='text-secondary-black-3'>
                {t('shareLink.viewAccess')}
              </Text>
              <div
                className='my-3 flex cursor-pointer items-center justify-between rounded-md p-2 hover:bg-primary-hover'
                onClick={() => {
                  setIsSecret(true)
                  handleAccess(true)
                }}>
                <div className='flex'>
                  <LockClosedIcon width={16} height={16} />
                  <div className='ml-2'>
                    <Text type={TextEnum.Body_medium}>{t('shareLink.secret')}</Text>
                    <Text type={TextEnum.Body_medium} className='mt-1 text-secondary-black-3'>
                      {t('shareLink.only')}
                    </Text>
                  </div>
                </div>
                {isSecret && <CheckIcon width={20} height={20} className='text-primary' />}
              </div>
              <div
                className='flex cursor-pointer items-center justify-between rounded-md px-2 py-3 hover:bg-primary-hover'
                onClick={() => {
                  setIsSecret(false)
                }}>
                <div className='flex h-5 items-center'>
                  <LockOpenIcon width={16} height={16} />
                  <div className='ml-2'>
                    <Text type={TextEnum.Body_medium}>{t('shareLink.public')}</Text>
                  </div>
                </div>
                {!isSecret && <CheckIcon width={20} height={20} className='text-primary' />}
              </div>
            </div>
            {!isSecret && (
              <div className='border-t border-[#E4E5EC] p-4'>
                {/* <Text type={TextEnum.Body_small} className='text-secondary-black-3'>
                  {t('shareLink.publicLink')}
                </Text> */}
                {/* <div className='flex items-center justify-between pt-4'>
                <div>{t('shareLink.indexing')}</div>
                <div>
                  <Switch
                    className='mt-2'
                    checked={indexing}
                    onCheckedChange={(value) => {
                      setIndexing(value)
                    }}
                  />
                </div>
              </div> */}
                {/* <div className='py-4'>
                  <Input
                    className='border-[#E4E5EC] bg-card'
                    value={copyText}
                    contentEditable={false}
                  />
                </div> */}
                {/* <CopyToClipboard text={copyText} onCopy={handleCopy}> */}
                <Button
                  className='w-full'
                  onClick={() => {
                    !linkCopied && handleCopy()
                  }}>
                  {!linkCopied ? (
                    <div>{t('shareLink.copyLink')}</div>
                  ) : (
                    <div className='flex items-center justify-center'>
                      <CheckIcon width={20} height={20} className='mr-2' />
                      <div>{t('shareLink.copySuccess')}</div>
                    </div>
                  )}
                </Button>
                {/* </CopyToClipboard> */}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </>
  )
}

export default ShareLink
