/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect } from 'react'
export const useResize = (setDropdownPosition: any, getElement: any, dropdownWidth) => {
  const handleResize = () => {
    const rect = getElement()
    if (rect) {
      setDropdownPosition({
        top: rect.bottom + 5 + window.scrollY,
        left: rect.left + window.scrollX + rect?.width / 2 - dropdownWidth / 2,
      })
    }
  }
  useEffect(() => {
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])
}
