/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect } from 'react'
export const useScroll = (setDropdownPosition: any, getElement: any, dropdownWidth) => {
  const handleScroll = () => {
    const rect = getElement()
    if (rect) {
      setDropdownPosition({
        top: rect.bottom + 5 + window.scrollY,
        left: rect.left + window.scrollX + rect?.width / 2 - dropdownWidth / 2,
      })
    }
  }
  useEffect(() => {
    const scrollDom = document.getElementById('ai-smarties-chat-content')
    scrollDom?.addEventListener('scroll', handleScroll)
    return () => {
      scrollDom?.removeEventListener('scroll', handleScroll)
    }
  }, [])
}
