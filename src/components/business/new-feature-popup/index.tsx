import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { XMarkIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

const POPUP_STORAGE_KEY = 'feature_popup_seen'

const FeaturePopup = () => {
  const { t } = useTranslation([Namespace.GLOBAL])
  const [open, setOpen] = useState(false)

  useEffect(() => {
    const lastSeen = localStorage.getItem(POPUP_STORAGE_KEY)
    const now = Date.now()

    if (!lastSeen) {
      setOpen(true)
      localStorage.setItem(POPUP_STORAGE_KEY, now.toString())
    }
  }, [])

  const handleClose = () => setOpen(false)

  if (!open) return null

  return (
    <div className='fixed bottom-6 right-6 w-[280px] rounded-[10px] border bg-card p-4'>
      <div className='flex items-center justify-between'>
        <Text type={TextEnum.H5}>{t('newFeature.online')}</Text>
        <XMarkIcon width={16} height={16} className='cursor-pointer' onClick={handleClose} />
      </div>
      <Image
        src='/images/newfeature.png'
        width={248}
        height={120}
        alt='New feature image'
        className='my-2'
      />
      <div>
        <Text type={TextEnum.H5} className='text-secondary-black-2'>
          {t('newFeature.title')}
        </Text>
        <Text type={TextEnum.Body_medium} className='mt-1 text-secondary-black-3'>
          {t('newFeature.content')}
        </Text>
      </div>
    </div>
  )
}

export default FeaturePopup
