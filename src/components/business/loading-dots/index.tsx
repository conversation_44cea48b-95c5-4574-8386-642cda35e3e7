import styles from './index.module.scss'
import cx from 'classnames'

export interface LoadingDotsProps {
  size?: number
}

const LoadingDots = ({ size = 6 }: LoadingDotsProps) => {
  const dots = [styles.dot1, styles.dot2, styles.dot3]

  return (
    <div className='flex'>
      {dots.map((dotClass, index) => (
        <div
          key={index}
          className={cx(styles.dot, dotClass)}
          style={{ width: size, height: size }}
        />
      ))}
    </div>
  )
}

export default LoadingDots
