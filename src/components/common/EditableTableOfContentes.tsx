import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react'
import { v4 as uuidv4 } from 'uuid'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import type { XYCoord, DropTargetMonitor } from 'react-dnd'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

// 拖拽项类型
const ITEM_TYPE = 'toc-item'

// 目录项类型定义
export interface TocItem {
  id: string
  title: string
  description: string
  level: number
  children: TocItem[]
  isSelected?: boolean
  isCollapsed?: boolean
  chapterIndex?: string
  isNewlyCreated?: boolean // 标识是否为新创建的章节，用于首次编辑时自动清空默认占位内容
}

// 拖拽位置类型
interface DragItem {
  id: string
  index: number
  level: number
  parentId: string | null
}

// 导出组件引用类型
export interface EditableTableOfContentsRef {
  getLatestData: () => TocItem[]
  expandAll: () => void
  collapseAll: () => void
  hideDescriptions: () => void
  showDescriptions: () => void
}

// 组件属性类型定义
interface EditableTableOfContentsProps {
  data: TocItem[]
  onChange: (items: TocItem[]) => void
  className?: string
  showPreview?: boolean
}

// 目录预览组件属性类型
interface TocPreviewProps {
  items: TocItem[]
  className?: string
  onItemClick: (id: string) => void
}

// 单个预览项属性类型
interface TocPreviewItemProps {
  item: TocItem
  level: number
  onItemClick: (id: string) => void
}

// 目录预览项组件
const TocPreviewItem: React.FC<TocPreviewItemProps> = ({ item, level, onItemClick }) => {
  const [isHovered, setIsHovered] = useState(false)

  if (!item.isSelected) {
    return null
  }

  return (
    <div className='mb-1'>
      <div
        className='group relative flex cursor-pointer items-center py-1'
        style={{ paddingLeft: `${level * 16}px` }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onItemClick(item.id)}>
        <span className='font-sm text-gray-800 transition-colors hover:text-blue-600'>
          {item.chapterIndex} {item.title}
        </span>

        {/* 描述提示框 */}
        {isHovered && item.description && (
          <div className='fixed z-50 ml-2 mt-16 w-64 translate-x-2 transform rounded-md border border-gray-200 bg-white p-2 shadow-md'>
            <p className='text-sm text-gray-600'>{item.description}</p>
          </div>
        )}
      </div>

      {item.children && !item.isCollapsed && (
        <div>
          {item.children.map((child) => (
            <TocPreviewItem
              key={child.id}
              item={child}
              level={level + 1}
              onItemClick={onItemClick}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// 目录预览组件
const TocPreview: React.FC<TocPreviewProps> = ({ items, className = '', onItemClick }) => {
  const { t } = useTranslation(Namespace.TOC)
  return (
    <div className={`${className}`}>
      <div className='h-full rounded-md border border-gray-200 bg-gray-50 p-4'>
        <h3 className='mb-4 text-lg font-medium text-gray-800'>{t('preview.title')}</h3>
        <div className='overflow-y-auto'>
          {items.map((item) => (
            <TocPreviewItem key={item.id} item={item} level={0} onItemClick={onItemClick} />
          ))}
        </div>
      </div>
    </div>
  )
}

// 单个目录项组件
interface TocItemComponentProps {
  item: TocItem
  level: number
  parentId: string | null
  index: number
  isSelected: boolean
  allChildrenSelected: boolean
  someChildrenSelected: boolean
  onSelect: (item: TocItem, selected: boolean) => void
  onTitleEdit: (id: string, newTitle: string) => void
  onDescriptionEdit: (id: string, newDescription: string) => void
  onMove: (dragId: string, hoverId: string, position: 'before' | 'after' | 'inside') => void
  onToggleCollapse: (id: string) => void
  onAddChild: (id: string) => void
  onAddSibling: (id: string) => void
  onDeleteNode: (id: string) => void
  renderChildren: (item: TocItem, level: number) => React.ReactNode
  forceHideDescription?: boolean
}

// 目录项组件
// eslint-disable-next-line react/display-name
const TocItemComponent = React.forwardRef<HTMLDivElement, TocItemComponentProps>(
  (
    {
      item,
      level,
      parentId,
      index,
      isSelected,
      allChildrenSelected,
      someChildrenSelected,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      onSelect,
      onTitleEdit,
      onDescriptionEdit,
      onMove,
      onToggleCollapse,
      onAddChild,
      onAddSibling,
      onDeleteNode,
      renderChildren,
      forceHideDescription = false,
    },
    ref,
  ) => {
    const { t } = useTranslation(Namespace.TOC)
    const localRef = useRef<HTMLDivElement>(null)
    const hasChildren = item.children.length > 0
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [showMenu, setShowMenu] = useState<boolean>(false)
    const [isHovered, setIsHovered] = useState(false)
    const [title, setTitle] = useState(item.title || '')
    const [description, setDescription] = useState(item.description || '')
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [showDescription, setShowDescription] = useState(true)

    // 合并外部和内部ref
    React.useImperativeHandle(ref, () => localRef.current!, [localRef])

    useEffect(() => {
      setTitle(item.title || '')
      setDescription(item.description || '')
    }, [item.title, item.description])

    // 选择框状态
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const checkboxState = allChildrenSelected
      ? 'checked'
      : someChildrenSelected
        ? 'indeterminate'
        : isSelected
          ? 'checked'
          : 'unchecked'

    // 放置位置状态
    const [dropPosition, setDropPosition] = useState<'before' | 'after' | 'inside' | null>(null)

    // 拖拽源配置
    const [{ isDragging }, drag] = useDrag({
      type: ITEM_TYPE,
      item: { id: item.id, index, level, parentId } as DragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
      end: () => {
        // 拖拽结束时清除dropPosition
        setDropPosition(null)
      },
    })

    // 放置目标配置
    const [{ isOver, canDrop }, drop] = useDrop({
      accept: ITEM_TYPE,
      collect: (monitor: DropTargetMonitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
      hover(dragObj: unknown, monitor: DropTargetMonitor) {
        const dragItem = dragObj as DragItem
        if (!localRef.current) return

        // 不能拖到自己上面
        if (dragItem.id === item.id) return

        // 获取拖拽项的位置信息
        const hoverBoundingRect = localRef.current.getBoundingClientRect()
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
        const clientOffset = monitor.getClientOffset() as XYCoord
        const hoverClientY = clientOffset.y - hoverBoundingRect.top

        // 确定放置位置（上方、下方或内部）
        // 上方1/4区域 - 放在前面
        // 下方1/4区域 - 放在后面
        // 中间区域 - 放在内部（成为子项）
        const topQuarter = hoverBoundingRect.height * 0.25
        const bottomQuarter = hoverBoundingRect.height * 0.75

        let position: 'before' | 'after' | 'inside' = 'inside'

        if (hoverClientY < topQuarter) {
          position = 'before'
        } else if (hoverClientY > bottomQuarter) {
          position = 'after'
        } else {
          position = 'inside'
        }

        // 更新当前放置位置状态用于视觉反馈
        setDropPosition(position)

        // 使用节流防止过于频繁的移动操作
        // 只在拖拽项进入目标或放置位置变化时执行移动
        if (monitor.isOver({ shallow: true })) {
          onMove(dragItem.id, item.id, position)
        }
      },
      drop: () => {
        // 放置完成后清除dropPosition
        setDropPosition(null)
      },
    })

    // 组合拖拽引用
    drag(drop(localRef))

    // 生成放置指示器的类名
    const getDropIndicatorClass = () => {
      if (!isOver || !canDrop || !dropPosition) return ''

      switch (dropPosition) {
        case 'before':
          return 'border-t-2 border-t-transparent pt-0.5'
        case 'after':
          return 'border-b-2 border-b-transparent pb-0.5'
        case 'inside':
          return 'bg-blue-500/10 shadow-[0_0_0_2px_rgba(59,130,246,0.4)]'
        default:
          return ''
      }
    }

    // 处理标题变化
    const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const newTitle = e.target.value
      setTitle(newTitle)
    }, [])

    // 处理标题获得焦点 - 新增章节首次编辑时清空默认内容
    const handleTitleFocus = useCallback(() => {
      if (item.isNewlyCreated && title === t('newChapter.title')) {
        setTitle('') // 清空默认标题"新章节"
        // 注意：不立即调用onTitleEdit，等到失焦时再保存，避免过早清除isNewlyCreated标识
      }
    }, [item.isNewlyCreated, title, t])

    // 处理标题失焦 - 保存标题更改
    const handleTitleBlur = useCallback(() => {
      if (title !== item.title) {
        onTitleEdit(item.id, title)
      }
    }, [item.id, onTitleEdit, title, item.title])

    // 处理描述变化
    const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newDescription = e.target.value
      setDescription(newDescription)
    }, [])

    // 处理描述获得焦点 - 新增章节首次编辑时清空默认内容
    const handleDescriptionFocus = useCallback(() => {
      if (item.isNewlyCreated && description === t('newChapter.description')) {
        setDescription('') // 清空默认描述"新章节描述"
        // 注意：不立即调用onDescriptionEdit，等到失焦时再保存，避免过早清除isNewlyCreated标识
      }
    }, [item.isNewlyCreated, description, t])

    // 处理描述失焦
    const handleDescriptionBlur = useCallback(() => {
      if (description !== item.description) {
        onDescriptionEdit(item.id, description)
      }
    }, [item.id, onDescriptionEdit, description, item.description])

    return (
      <div
        ref={localRef}
        className={`my-1 transition-all duration-200 ease-in-out ${level === 0 ? 'my-3' : 'my-1'} ${isDragging ? 'opacity-50' : ''} ${!isSelected ? 'text-gray-400' : ''} ${getDropIndicatorClass()}`}
        style={{
          cursor: 'move',
          position: 'relative',
          opacity: isSelected ? 1 : 0.7,
        }}
        data-level={level}
        onMouseEnter={() => {
          setShowMenu(true)
          setIsHovered(true)
        }}
        onMouseLeave={() => {
          setShowMenu(false)
          setIsHovered(false)
        }}>
        {/* 前置指示线 */}
        {isOver && canDrop && dropPosition === 'before' && (
          <div className='absolute -top-[2px] left-0 right-0 z-10 h-1 bg-blue-500'></div>
        )}

        <div
          className='toc-item-content flex flex-col py-2'
          style={{ paddingLeft: `${level * 16}px` }}>
          {/* 按钮区域 - 将所有按钮放在标题上方 */}
          <div className='buttons-row mb-2 flex items-center justify-between'>
            {/* 永久显示的按钮 - 左侧 */}
            <div className='always-visible-buttons flex items-center'>
              {/* 展开/折叠按钮 - 只有有子项时才显示 */}
              {hasChildren && (
                <button
                  className={`toc-collapse-button mr-2 flex items-center ${!isSelected ? 'text-gray-400' : ''}`}
                  onClick={(e) => {
                    e.stopPropagation()
                    onToggleCollapse(item.id)
                  }}
                  title={item.isCollapsed ? t('item.toggleExpand') : t('item.toggleCollapse')}>
                  <svg
                    width='16'
                    height='16'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                    strokeLinecap='round'
                    strokeLinejoin='round'>
                    {item.isCollapsed ? (
                      <polyline points='9 18 15 12 9 6' />
                    ) : (
                      <polyline points='6 9 12 15 18 9' />
                    )}
                  </svg>
                  <span className={`ml-1 text-xs ${!isSelected ? 'text-gray-400' : ''}`}>
                    {item.isCollapsed ? t('item.toggleExpand') : t('item.toggleCollapse')}
                  </span>
                </button>
              )}

              {/* 复选框 */}
              {/* <div className="checkbox-wrapper mr-3 flex items-center">
                            <input
                                type="checkbox"
                                checked={checkboxState === 'checked'}
                                ref={el => {
                                    if (el) {
                                        el.indeterminate = checkboxState === 'indeterminate'
                                    }
                                }}
                                onChange={e => onSelect(item, e.target.checked)}
                                className="cursor-pointer"
                            />
                            <span className={`ml-1 text-xs ${!isSelected ? 'text-gray-400' : ''}`}>Select</span>
                        </div> */}

              {/* 悬停时显示的按钮 */}
              <div
                className='hover-buttons flex items-center gap-2'
                style={{
                  opacity: isHovered ? 1 : 0,
                  transition: 'opacity 0.2s',
                }}>
                <button
                  className='toc-add-button flex items-center'
                  onClick={(e) => {
                    e.stopPropagation()
                    onAddChild(item.id)
                  }}
                  title={t('item.addChild')}>
                  <svg
                    width='16'
                    height='16'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                    strokeLinecap='round'
                    strokeLinejoin='round'>
                    <path d='M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2'></path>
                    <circle cx='9' cy='7' r='4'></circle>
                    <line x1='19' y1='8' x2='19' y2='14'></line>
                    <line x1='16' y1='11' x2='22' y2='11'></line>
                  </svg>
                  <span className='ml-1 text-xs'>{t('item.addChild')}</span>
                </button>

                <button
                  className='toc-add-sibling-button flex items-center'
                  onClick={(e) => {
                    e.stopPropagation()
                    onAddSibling(item.id)
                  }}
                  title={t('item.addSibling')}>
                  <svg
                    width='16'
                    height='16'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                    strokeLinecap='round'
                    strokeLinejoin='round'>
                    <path d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'></path>
                    <circle cx='9' cy='7' r='4'></circle>
                    <path d='M23 21v-2a4 4 0 0 0-3-3.87'></path>
                    <path d='M16 3.13a4 4 0 0 1 0 7.75'></path>
                  </svg>
                  <span className='ml-1 text-xs'>{t('item.addSibling')}</span>
                </button>

                <button
                  className='toc-delete-button flex items-center'
                  onClick={(e) => {
                    e.stopPropagation()
                    onDeleteNode(item.id)
                  }}
                  title={t('item.deleteNode')}>
                  <svg
                    width='16'
                    height='16'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                    strokeLinecap='round'
                    strokeLinejoin='round'>
                    <polyline points='3 6 5 6 21 6' />
                    <path d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2' />
                    <line x1='10' y1='11' x2='10' y2='17' />
                    <line x1='14' y1='11' x2='14' y2='17' />
                  </svg>
                  <span className='ml-1 text-xs'>{t('item.deleteNode')}</span>
                </button>
              </div>
            </div>

            {/* 章节控制区 - 靠右 */}
            <div className='chapter-controls flex items-center gap-2'>
              {/* 章节号 */}
              <div
                className={`chapter-number font-sm text-xs ${isSelected ? 'text-primary-500' : 'text-gray-400'}`}>
                {t('item.chapterNumber')} {item.chapterIndex || ''}
              </div>
            </div>
          </div>

          {/* 标题和描述 */}
          <div className='toc-item-text flex-grow'>
            {/* 标题 */}
            <div className='title-area mb-1.5'>
              <input
                type='text'
                value={title}
                onChange={handleTitleChange}
                onFocus={handleTitleFocus} // 新创建章节聚焦时清空默认标题
                onBlur={handleTitleBlur}
                className={`w-full rounded border px-2 py-1 font-medium focus:outline-none focus:ring-2 focus:ring-blue-300 ${!isSelected ? 'border-gray-200 text-gray-400' : 'text-black'}`}
                placeholder={t('item.titlePlaceholder')}
              />
            </div>

            {/* 描述 - 根据显示状态来展示或隐藏 */}
            {showDescription && !forceHideDescription && (
              <div className='description-area'>
                <textarea
                  value={description}
                  onChange={handleDescriptionChange}
                  onFocus={handleDescriptionFocus} // 新创建章节聚焦时清空默认描述
                  onBlur={handleDescriptionBlur}
                  className={`w-full rounded border border-gray-200 px-2 py-1 text-sm font-normal text-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-300 ${!isSelected ? 'border-gray-100 text-gray-400' : 'text-black'}`}
                  rows={2}
                  placeholder={t('item.descriptionPlaceholder')}
                />
              </div>
            )}
          </div>
        </div>

        {/* 内部指示框 */}
        {isOver && canDrop && dropPosition === 'inside' && (
          <div className='pointer-events-none absolute inset-0 rounded-md border-2 border-blue-500'></div>
        )}

        {/* 后置指示线 */}
        {isOver && canDrop && dropPosition === 'after' && (
          <div className='absolute -bottom-[2px] left-0 right-0 z-10 h-1 bg-blue-500'></div>
        )}

        {/* 渲染子项 - 当不折叠时才显示 */}
        {hasChildren && !item.isCollapsed && (
          <div className='children-container'>{renderChildren(item, level + 1)}</div>
        )}
      </div>
    )
  },
)

// eslint-disable-next-line react/display-name
const EditableTableOfContents = forwardRef<
  EditableTableOfContentsRef,
  EditableTableOfContentsProps
>(({ data, onChange, className = '', showPreview = true }, ref) => {
  const { t } = useTranslation(Namespace.TOC)
  // 获取所有项的ID
  const getAllItemIds = useCallback((items: TocItem[]): string[] => {
    return items.reduce((ids: string[], item) => {
      return [...ids, item.id, ...getAllItemIds(item.children)]
    }, [])
  }, [])

  // 当前选中的项 - 初始化为所有项
  const [selectedItems, setSelectedItems] = useState<string[]>(() => getAllItemIds(data))
  // 是否隐藏所有章节概要
  const [isAllDescriptionHidden, setIsAllDescriptionHidden] = useState<boolean>(false)
  // 处理过的数据（包含选中状态）
  const [processedData, setProcessedData] = useState<TocItem[]>([])

  // 创建对象引用映射，用于滚动到指定元素
  const tocItemRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  // 处理数据，添加选中状态
  const processData = useCallback(
    (items: TocItem[]): TocItem[] => {
      return items.map((item) => {
        const isSelected = selectedItems.includes(item.id)
        return {
          ...item,
          isSelected: isSelected,
          children: processData(item.children),
        }
      })
    },
    [selectedItems],
  )

  // 更新处理过的数据
  useEffect(() => {
    setProcessedData(processData(data))
  }, [data, selectedItems, processData])

  // 当data变化时重新计算选中状态
  useEffect(() => {
    // 获取当前所有项的ID
    const allIds = getAllItemIds(data)

    // 如果是初始化或者数据完全变化，重新设置选中所有项
    if (
      selectedItems.length === 0 ||
      (data.length > 0 && selectedItems.some((id) => !allIds.includes(id)))
    ) {
      setSelectedItems(allIds)
    }
  }, [data, getAllItemIds, selectedItems])

  // 判断是否所有子项都被选中
  const areAllChildrenSelected = useCallback(
    (item: TocItem): boolean => {
      if (item.children.length === 0) return selectedItems.includes(item.id)

      return (
        item.children.length > 0 && item.children.every((child) => areAllChildrenSelected(child))
      )
    },
    [selectedItems],
  )

  // 判断是否部分子项被选中
  const areSomeChildrenSelected = useCallback(
    (item: TocItem): boolean => {
      if (selectedItems.includes(item.id)) return true
      if (item.children.length === 0) return false

      return item.children.some((child) => areSomeChildrenSelected(child))
    },
    [selectedItems],
  )

  // 处理选择项操作
  const handleSelect = useCallback(
    (item: TocItem, selected: boolean) => {
      // 获取当前项及其所有子项的ID
      const getIdsToSelect = (currentItem: TocItem): string[] => {
        const ids = [currentItem.id]
        currentItem.children.forEach((child) => {
          ids.push(...getIdsToSelect(child))
        })
        return ids
      }

      // 当前项及其所有子项的ID
      const idsToProcess = getIdsToSelect(item)

      let newSelectedItems: string[]

      if (selected) {
        // 添加当前项及其所有子项
        newSelectedItems = [...new Set([...selectedItems, ...idsToProcess])]

        // 如果选中的是新创建的章节，自动清空默认内容
        if (item.isNewlyCreated) {
          const shouldClearTitle = item.title === t('newChapter.title')
          const shouldClearDescription = item.description === t('newChapter.description')

          if (shouldClearTitle || shouldClearDescription) {
            // 直接更新数据，清空默认内容并清除新创建标识
            const updateContent = (items: TocItem[]): TocItem[] => {
              return items.map((currentItem) => {
                if (currentItem.id === item.id) {
                  return {
                    ...currentItem,
                    title: shouldClearTitle ? '' : currentItem.title,
                    description: shouldClearDescription ? '' : currentItem.description,
                    isNewlyCreated: false,
                  }
                }
                if (currentItem.children.length > 0) {
                  return { ...currentItem, children: updateContent(currentItem.children) }
                }
                return currentItem
              })
            }
            onChange(updateContent(data))
          }
        }
      } else {
        // 移除当前项及其所有子项
        newSelectedItems = selectedItems.filter((id) => !idsToProcess.includes(id))
      }

      setSelectedItems(newSelectedItems)
    },
    [selectedItems, data, onChange, t],
  )

  // 处理编辑标题
  const handleTitleEdit = useCallback(
    (id: string, newTitle: string) => {
      const updateTitle = (items: TocItem[]): TocItem[] => {
        return items.map((item) => {
          if (item.id === id) {
            // 编辑标题时清除新创建标识，后续编辑不再自动清空内容
            return { ...item, title: newTitle, isNewlyCreated: false }
          }
          if (item.children.length > 0) {
            return { ...item, children: updateTitle(item.children) }
          }
          return item
        })
      }

      const newData = updateTitle(data)
      onChange(newData)
    },
    [data, onChange],
  )

  // 处理编辑描述
  const handleDescriptionEdit = useCallback(
    (id: string, newDescription: string) => {
      const updateDescription = (items: TocItem[]): TocItem[] => {
        return items.map((item) => {
          if (item.id === id) {
            // 编辑描述时清除新创建标识，后续编辑不再自动清空内容
            return { ...item, description: newDescription, isNewlyCreated: false }
          }
          if (item.children.length > 0) {
            return { ...item, children: updateDescription(item.children) }
          }
          return item
        })
      }

      const newData = updateDescription(data)
      onChange(newData)
    },
    [data, onChange],
  )

  // 删除选中的项
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleDeleteSelected = useCallback(() => {
    if (selectedItems.length === 0) return

    const removeSelected = (items: TocItem[]): TocItem[] => {
      // 过滤掉被选中的顶层项
      let filteredItems = items.filter((item) => !selectedItems.includes(item.id))

      // 递归处理子项
      filteredItems = filteredItems.map((item) => {
        if (item.children.length > 0) {
          return { ...item, children: removeSelected(item.children) }
        }
        return item
      })

      return filteredItems
    }

    const newData = removeSelected(data)
    onChange(newData)
    setSelectedItems([]) // 清空选中项
  }, [data, onChange, selectedItems])

  // 删除单个节点
  const handleDeleteNode = useCallback(
    (nodeId: string) => {
      const removeNode = (items: TocItem[]): TocItem[] => {
        // 过滤掉当前节点
        let filteredItems = items.filter((item) => item.id !== nodeId)

        // 递归处理子项
        filteredItems = filteredItems.map((item) => {
          if (item.children.length > 0) {
            return { ...item, children: removeNode(item.children) }
          }
          return item
        })

        return filteredItems
      }

      const newData = removeNode(data)
      onChange(newData)

      // 如果被删除的节点在已选中列表中，也需要从中移除
      if (selectedItems.includes(nodeId)) {
        setSelectedItems(selectedItems.filter((id) => id !== nodeId))
      }
    },
    [data, onChange, selectedItems],
  )

  // 移动项目
  const handleMove = useCallback(
    (dragId: string, targetId: string, position: 'before' | 'after' | 'inside') => {
      // 如果是同一个项目，不处理
      if (dragId === targetId) return

      // 创建数据副本
      const dataCopy = JSON.parse(JSON.stringify(data))

      // 查找要移动的项目和目标项目
      let dragItem: TocItem | null = null
      let dragParent: TocItem[] | null = null
      let targetItem: TocItem | null = null
      let targetParent: TocItem[] | null = null

      // 递归查找项目
      const findItem = (
        items: TocItem[],
        id: string,
        parent: TocItem[] | null = null,
      ): { item: TocItem | null; parent: TocItem[] | null } => {
        for (const item of items) {
          if (item.id === id) {
            return { item, parent: parent || items }
          }

          if (item.children && item.children.length > 0) {
            const result = findItem(item.children, id, item.children)
            if (result.item) return result
          }
        }

        return { item: null, parent: null }
      }

      // 在拷贝的数据中查找项目
      const dragResult = findItem(dataCopy, dragId)
      const targetResult = findItem(dataCopy, targetId)

      if (!dragResult.item || !targetResult.item || !dragResult.parent || !targetResult.parent) {
        return
      }

      dragItem = dragResult.item
      dragParent = dragResult.parent
      targetItem = targetResult.item
      targetParent = targetResult.parent

      // 检查是否尝试将项目拖放到自己的后代
      const isDescendant = (ancestor: TocItem, descendant: TocItem): boolean => {
        if (!ancestor.children || ancestor.children.length === 0) return false

        for (const child of ancestor.children) {
          if (child.id === descendant.id) return true
          if (isDescendant(child, descendant)) return true
        }

        return false
      }

      if (isDescendant(dragItem, targetItem)) {
        return // 防止创建循环结构
      }

      // 从原始位置删除项目
      const dragIndex = dragParent.findIndex((item) => item.id === dragId)
      if (dragIndex !== -1) {
        dragParent.splice(dragIndex, 1)
      }

      // 根据指定位置放置项目
      if (position === 'inside') {
        // 调整拖拽项层级
        dragItem.level = targetItem.level + 1

        // 确保目标有children数组
        if (!targetItem.children) targetItem.children = []

        // 添加到目标的子项中
        targetItem.children.push(dragItem)
      } else if (position === 'before' || position === 'after') {
        // 调整拖拽项层级
        dragItem.level = targetItem.level

        // 查找目标在其父级中的索引
        const targetIndex = targetParent.findIndex((item) => item.id === targetId)

        if (targetIndex !== -1) {
          // 根据位置插入
          const insertPosition = position === 'before' ? targetIndex : targetIndex + 1
          targetParent.splice(insertPosition, 0, dragItem)
        }
      }

      // 更新数据
      onChange(dataCopy)
    },
    [data, onChange],
  )

  // 切换单个目录项的折叠状态
  const handleToggleCollapse = useCallback(
    (id: string) => {
      const toggleCollapse = (items: TocItem[]): TocItem[] => {
        return items.map((item) => {
          if (item.id === id) {
            return { ...item, isCollapsed: !item.isCollapsed }
          }
          if (item.children.length > 0) {
            return { ...item, children: toggleCollapse(item.children) }
          }
          return item
        })
      }

      const newData = toggleCollapse(data)
      onChange(newData)
    },
    [data, onChange],
  )

  // 展开所有章节
  const handleExpandAll = useCallback(() => {
    const updateItems = (items: TocItem[]): TocItem[] => {
      return items.map((item) => {
        if (item.children.length > 0) {
          return {
            ...item,
            isCollapsed: false,
            children: updateItems(item.children),
          }
        }
        return item
      })
    }

    const newData = updateItems(data)
    onChange(newData)
  }, [data, onChange])

  // 收起所有章节
  const handleCollapseAll = useCallback(() => {
    const updateItems = (items: TocItem[]): TocItem[] => {
      return items.map((item) => {
        if (item.children.length > 0) {
          return {
            ...item,
            isCollapsed: true,
            children: updateItems(item.children),
          }
        }
        return item
      })
    }

    const newData = updateItems(data)
    onChange(newData)
  }, [data, onChange])

  // 隐藏所有章节描述
  const handleHideDescriptions = useCallback(() => {
    setIsAllDescriptionHidden(true)
  }, [])

  // 显示所有章节描述
  const handleShowDescriptions = useCallback(() => {
    setIsAllDescriptionHidden(false)
  }, [])

  // 添加子节点 - 自动清空标题和描述
  const handleAddChild = useCallback(
    (parentId: string) => {
      const newNode: TocItem = {
        id: uuidv4(),
        title: t('newChapter.title'), // 初始显示"新章节"
        description: t('newChapter.description'), // 初始显示"新章节描述"
        level: 0, // 临时值，将在下面更新
        children: [],
        isNewlyCreated: true, // 标识为新创建的子章节，用于自动清空默认内容
      }

      const addChildToItem = (items: TocItem[]): TocItem[] => {
        return items.map((item) => {
          if (item.id === parentId) {
            // 设置正确的层级
            newNode.level = item.level + 1

            // 添加为子节点
            return {
              ...item,
              children: [...item.children, newNode],
              // 如果父节点是折叠的，展开它
              isCollapsed: false,
            }
          }

          if (item.children.length > 0) {
            return {
              ...item,
              children: addChildToItem(item.children),
            }
          }

          return item
        })
      }

      const newData = addChildToItem(data)

      // 立即清空新创建子章节的标题和描述，实现用户体验优化
      const clearNewChapterContent = (items: TocItem[]): TocItem[] => {
        return items.map((item) => {
          if (item.id === newNode.id && item.isNewlyCreated) {
            return {
              ...item,
              title: '', // 清空"新章节"标题，准备用户输入
              description: '', // 清空"新章节描述"内容，准备用户输入
              isNewlyCreated: false, // 清除新创建标识，防止重复清空
            }
          }
          if (item.children.length > 0) {
            return {
              ...item,
              children: clearNewChapterContent(item.children),
            }
          }
          return item
        })
      }

      // 应用清空内容后的最终数据
      const finalData = clearNewChapterContent(newData)
      onChange(finalData)

      // 自动选中新创建的子章节，便于用户直接编辑
      setSelectedItems((prev) => [...prev, newNode.id])
    },
    [data, onChange, setSelectedItems, t],
  )

  // 添加同级节点 - 自动清空标题和描述
  const handleAddSibling = useCallback(
    (siblingId: string) => {
      // 创建新节点
      const newNode: TocItem = {
        id: uuidv4(),
        title: t('newChapter.title'), // 初始显示"新章节"
        description: t('newChapter.description'), // 初始显示"新章节描述"
        level: 0, // 临时值，将在下面更新
        children: [],
        isNewlyCreated: true, // 标识为新创建的同级章节，用于自动清空默认内容
      }

      // 查找父级和位置
      let parentId: string | null = null
      let siblingIndex = -1

      const findParentAndIndex = (
        items: TocItem[],
        id: string,
        parent: string | null = null,
      ): { found: boolean; parentId: string | null; index: number } => {
        for (let i = 0; i < items.length; i++) {
          if (items[i].id === id) {
            return { found: true, parentId: parent, index: i }
          }

          if (items[i].children.length > 0) {
            const result = findParentAndIndex(items[i].children, id, items[i].id)
            if (result.found) return result
          }
        }

        return { found: false, parentId: null, index: -1 }
      }

      const {
        found,
        parentId: foundParentId,
        index: foundIndex,
      } = findParentAndIndex(data, siblingId)

      if (found) {
        parentId = foundParentId
        siblingIndex = foundIndex
      } else {
        return // 未找到参考节点，无法添加
      }

      // 添加同级节点
      const addSiblingToItems = (items: TocItem[], targetParentId: string | null): TocItem[] => {
        // 如果是顶层节点
        if (targetParentId === null) {
          // 设置正确的层级
          newNode.level = items[siblingIndex].level

          // 在目标节点后面插入新节点
          const newItems = [...items]
          newItems.splice(siblingIndex + 1, 0, newNode)
          return newItems
        }

        // 处理子节点的情况
        return items.map((item) => {
          if (item.id === targetParentId) {
            // 设置正确的层级
            newNode.level = item.children[siblingIndex].level

            // 复制子节点列表并插入新节点
            const newChildren = [...item.children]
            newChildren.splice(siblingIndex + 1, 0, newNode)

            return {
              ...item,
              children: newChildren,
            }
          }

          if (item.children.length > 0) {
            return {
              ...item,
              children: addSiblingToItems(item.children, targetParentId),
            }
          }

          return item
        })
      }

      const newData = addSiblingToItems(data, parentId)

      // 立即清空新创建同级章节的标题和描述，实现用户体验优化
      const clearNewChapterContent = (items: TocItem[]): TocItem[] => {
        return items.map((item) => {
          if (item.id === newNode.id && item.isNewlyCreated) {
            return {
              ...item,
              title: '', // 清空"新章节"标题，准备用户输入
              description: '', // 清空"新章节描述"内容，准备用户输入
              isNewlyCreated: false, // 清除新创建标识，防止重复清空
            }
          }
          if (item.children.length > 0) {
            return {
              ...item,
              children: clearNewChapterContent(item.children),
            }
          }
          return item
        })
      }

      // 应用清空内容后的最终数据
      const finalData = clearNewChapterContent(newData)
      onChange(finalData)

      // 自动选中新创建的同级章节，便于用户直接编辑
      setSelectedItems((prev) => [...prev, newNode.id])
    },
    [data, onChange, setSelectedItems, t],
  )

  // 生成章节号
  const generateChapterNumbers = useCallback(
    (items: TocItem[], prefix = '', result: Record<string, string> = {}) => {
      const updatedItems = [...items]
      updatedItems.forEach((item, index) => {
        const chapterIndex = prefix ? `${prefix}.${index + 1}` : `${index + 1}`
        result[item.id] = chapterIndex

        // 更新当前项的章节索引
        item.chapterIndex = chapterIndex

        // 递归处理子项
        if (item.children && item.children.length > 0) {
          generateChapterNumbers(item.children, chapterIndex, result)
        }
      })

      return result
    },
    [],
  )

  // 存储每个章节的索引 - 使用原始数据引用而不是深拷贝，避免触发额外的更新
  const chapterIndices = generateChapterNumbers(data)

  // 在useEffect中监听data变化，只在结构变化时更新章节号
  useEffect(() => {
    // 计算章节号但不触发onChange
    generateChapterNumbers(data)
  }, [data, generateChapterNumbers])

  // 处理预览区项目点击
  const handlePreviewItemClick = useCallback((id: string) => {
    // 找到对应的编辑区元素并滚动到相应位置
    const element = tocItemRefs.current[id]
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })

      // 添加一个临时高亮效果
      element.classList.add('animate-pulse', 'bg-blue-500/10')
      setTimeout(() => {
        element.classList.remove('animate-pulse', 'bg-blue-500/10')
      }, 2000)
    }
  }, [])

  // 创建引用设置函数
  const setTocItemRef = useCallback((id: string, element: HTMLDivElement | null) => {
    tocItemRefs.current[id] = element
  }, [])

  // 渲染目录子项逻辑
  const renderTocItemChildren = useCallback(
    (parentItem: TocItem, level: number) => {
      return parentItem.children.map((child, index) => {
        const isSelected = selectedItems.includes(child.id)
        const allChildrenSelected = areAllChildrenSelected(child)
        const someChildrenSelected = areSomeChildrenSelected(child) && !allChildrenSelected

        // 添加章节索引
        const itemWithIndex = {
          ...child,
          chapterIndex: chapterIndices[child.id],
        }

        return (
          <TocItemComponent
            key={child.id}
            ref={(el) => setTocItemRef(child.id, el as HTMLDivElement)}
            item={itemWithIndex}
            level={level}
            parentId={parentItem.id}
            index={index}
            isSelected={isSelected}
            allChildrenSelected={allChildrenSelected}
            someChildrenSelected={someChildrenSelected}
            onSelect={handleSelect}
            onTitleEdit={handleTitleEdit}
            onDescriptionEdit={handleDescriptionEdit}
            onMove={handleMove}
            onToggleCollapse={handleToggleCollapse}
            onAddChild={handleAddChild}
            onAddSibling={handleAddSibling}
            onDeleteNode={handleDeleteNode}
            renderChildren={(item, level) => renderTocItemChildren(item, level)}
            forceHideDescription={isAllDescriptionHidden}
          />
        )
      })
    },
    [
      selectedItems,
      areAllChildrenSelected,
      areSomeChildrenSelected,
      handleSelect,
      handleTitleEdit,
      handleDescriptionEdit,
      handleMove,
      handleToggleCollapse,
      handleAddChild,
      handleAddSibling,
      handleDeleteNode,
      chapterIndices,
      isAllDescriptionHidden,
      setTocItemRef,
    ],
  )

  // 渲染顶层目录项
  const renderTocItems = useCallback(() => {
    return data.map((item, index) => {
      const isSelected = selectedItems.includes(item.id)
      const allChildrenSelected = areAllChildrenSelected(item)
      const someChildrenSelected = areSomeChildrenSelected(item) && !allChildrenSelected

      // 添加章节索引
      const itemWithIndex = {
        ...item,
        chapterIndex: chapterIndices[item.id],
      }

      return (
        <TocItemComponent
          key={item.id}
          ref={(el) => setTocItemRef(item.id, el as HTMLDivElement)}
          item={itemWithIndex}
          level={0}
          parentId={null}
          index={index}
          isSelected={isSelected}
          allChildrenSelected={allChildrenSelected}
          someChildrenSelected={someChildrenSelected}
          onSelect={handleSelect}
          onTitleEdit={handleTitleEdit}
          onDescriptionEdit={handleDescriptionEdit}
          onMove={handleMove}
          onToggleCollapse={handleToggleCollapse}
          onAddChild={handleAddChild}
          onAddSibling={handleAddSibling}
          onDeleteNode={handleDeleteNode}
          renderChildren={(item, level) => renderTocItemChildren(item, level)}
          forceHideDescription={isAllDescriptionHidden}
        />
      )
    })
  }, [
    data,
    selectedItems,
    areAllChildrenSelected,
    areSomeChildrenSelected,
    handleSelect,
    handleTitleEdit,
    handleDescriptionEdit,
    handleMove,
    handleToggleCollapse,
    handleAddChild,
    handleAddSibling,
    handleDeleteNode,
    chapterIndices,
    isAllDescriptionHidden,
    setTocItemRef,
  ])

  // 暴露获取最新数据的方法
  useImperativeHandle(
    ref,
    () => ({
      getLatestData: () => {
        return processData(data)
      },
      expandAll: handleExpandAll,
      collapseAll: handleCollapseAll,
      hideDescriptions: handleHideDescriptions,
      showDescriptions: handleShowDescriptions,
    }),
    [
      data,
      processData,
      handleExpandAll,
      handleCollapseAll,
      handleHideDescriptions,
      handleShowDescriptions,
    ],
  )

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`editable-table-of-contents ${className}`}>
        {showPreview ? (
          <div className='flex flex-wrap gap-4 md:flex-nowrap'>
            {/* 预览区 */}
            <div className='w-full md:w-1/3'>
              <TocPreview items={processedData} onItemClick={handlePreviewItemClick} />
            </div>

            {/* 编辑区 */}
            <div className='w-full overflow-x-auto md:w-2/3'>
              {/* 章节内容区域 */}
              <div className='toc-content min-w-max rounded-lg border p-4'>
                {data.length > 0 ? (
                  <>{renderTocItems()}</>
                ) : (
                  <div className='empty-state py-8 text-center text-gray-500'>
                    <p className='mb-4'>{t('emptyState.noData')}</p>
                    <button
                      onClick={() => {
                        const newNode: TocItem = {
                          id: uuidv4(),
                          title: t('firstChapter.title'),
                          description: t('firstChapter.description'),
                          level: 0,
                          children: [],
                        }
                        onChange([newNode])
                      }}
                      className='rounded-md bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600'>
                      {t('emptyState.addFirst')}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* 章节内容区域 */}
            <div className='toc-content rounded-lg border p-4'>
              {data.length > 0 ? (
                <>{renderTocItems()}</>
              ) : (
                <div className='empty-state py-8 text-center text-gray-500'>
                  <p className='mb-4'>{t('emptyState.noData')}</p>
                  <button
                    onClick={() => {
                      const newNode: TocItem = {
                        id: uuidv4(),
                        title: t('firstChapter.title'),
                        description: t('firstChapter.description'),
                        level: 0,
                        children: [],
                      }
                      onChange([newNode])
                    }}
                    className='rounded-md bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600'>
                    {t('emptyState.addFirst')}
                  </button>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </DndProvider>
  )
})

export default EditableTableOfContents
