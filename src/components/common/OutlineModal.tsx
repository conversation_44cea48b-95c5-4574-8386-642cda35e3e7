import React, { useState, useRef, useEffect } from 'react'
import { X } from 'lucide-react'
import dynamic from 'next/dynamic'
import type { TocItem, EditableTableOfContentsRef } from './EditableTableOfContentes'
import Countdown from './Countdown'

// 动态导入EditableTableOfContents组件，避免SSR问题
const EditableTableOfContents = dynamic(() => import('./EditableTableOfContentes'), {
  ssr: false,
  loading: () => <div>Loading...</div>,
})

interface OutlineModalProps {
  open: boolean
  onClose: () => void
  outlineData: TocItem[]
  onConfirm: (outline: TocItem[]) => void
  onBack: () => void
  isLoading?: boolean
  originalQuery?: string
  confirmationText?: string
}

const OutlineModal: React.FC<OutlineModalProps> = ({
  open,
  onClose,
  outlineData,
  onConfirm,
  onBack,
  isLoading = false,
}) => {
  const [selectedOutline, setSelectedOutline] = useState<TocItem[]>(outlineData)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isExpanded, setIsExpanded] = useState(true)
  const [areDescriptionsHidden, setAreDescriptionsHidden] = useState(false)
  const [isComponentLoaded, setIsComponentLoaded] = useState(false)
  const outlineRef = useRef<EditableTableOfContentsRef>(null)

  // 同步outlineData到selectedOutline
  useEffect(() => {
    if (outlineData && outlineData.length > 0) {
      setSelectedOutline(outlineData)
    }
  }, [outlineData])

  // 处理大纲数据变化
  const handleOutlineChange = (newOutline: TocItem[]) => {
    setSelectedOutline(newOutline)
  }

  // 处理确认提交
  const handleConfirm = async () => {
    if (!selectedOutline || selectedOutline.length === 0) {
      // 可以添加错误提示
      return
    }

    setIsSubmitting(true)
    try {
      await onConfirm(selectedOutline)
    } catch (error) {
      console.error('大纲确认失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理展开/收起所有
  const handleToggleExpandAll = () => {
    if (!isComponentLoaded) return

    if (isExpanded) {
      outlineRef.current?.collapseAll()
    } else {
      outlineRef.current?.expandAll()
    }
    setIsExpanded(!isExpanded)
  }

  // 处理显示/隐藏描述
  const handleToggleDescriptions = () => {
    if (!isComponentLoaded) return

    if (areDescriptionsHidden) {
      outlineRef.current?.showDescriptions()
    } else {
      outlineRef.current?.hideDescriptions()
    }
    setAreDescriptionsHidden(!areDescriptionsHidden)
  }

  // 检查组件是否已经加载
  useEffect(() => {
    const checkComponentLoaded = () => {
      if (
        outlineRef.current &&
        typeof outlineRef.current.collapseAll === 'function' &&
        typeof outlineRef.current.expandAll === 'function' &&
        typeof outlineRef.current.hideDescriptions === 'function' &&
        typeof outlineRef.current.showDescriptions === 'function'
      ) {
        setIsComponentLoaded(true)
      }
    }

    // 立即检查
    checkComponentLoaded()

    // 如果还没有加载，设置一个定时器继续检查
    if (!isComponentLoaded) {
      const timer = setInterval(checkComponentLoaded, 100)
      return () => clearInterval(timer)
    }
  }, [isComponentLoaded])

  // 关闭模态框时重置状态
  useEffect(() => {
    if (!open) {
      setIsExpanded(true)
      setAreDescriptionsHidden(false)
      setIsComponentLoaded(false)
    }
  }, [open])

  if (!open) return null

  return (
    <div
      className='fixed inset-0 z-50 overflow-y-auto'
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div className='mx-auto min-h-screen w-[1100px] bg-white'>
        {/* 头部 */}
        <div className='flex items-center justify-between border-b border-gray-200 px-6 py-4'>
          <div className='flex flex-1 items-center justify-between'>
            <div>
              <h2 className='text-2xl font-semibold text-gray-900'>Confirm Report Outline</h2>
              <p className='mt-1 text-sm text-gray-600'>
                Review and customize the report outline before starting the research
              </p>
            </div>
            <div className='flex items-center space-x-2'>
              <button
                onClick={handleToggleExpandAll}
                className='flex items-center rounded-md border border-gray-300 bg-white px-3 py-1 text-sm text-gray-700 hover:bg-gray-50'>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  className='mr-1 h-4 w-4'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'>
                  {isExpanded ? (
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M20 12H4'
                    />
                  ) : (
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5'
                    />
                  )}
                </svg>
                {isExpanded ? 'Collapse All' : 'Expand All'}
              </button>
              <button
                onClick={handleToggleDescriptions}
                className='flex items-center rounded-md border border-gray-300 bg-white px-3 py-1 text-sm text-gray-700 hover:bg-gray-50'>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  className='mr-1 h-4 w-4'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'>
                  {areDescriptionsHidden ? (
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  ) : (
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  )}
                </svg>
                {areDescriptionsHidden ? 'Show Descriptions' : 'Hide Descriptions'}
              </button>
            </div>
          </div>
          <button
            onClick={onClose}
            className='rounded-full p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600'>
            <X className='h-5 w-5' />
          </button>
        </div>

        {/* 内容区域 */}
        <div className='flex gap-8 pl-8'>
          {/* 左侧：大纲编辑器 */}
          <div className='flex-1 p-6'>
            <div className='mb-4 flex items-center'>
              <div className='flex items-center space-x-2 text-sm text-gray-500'>
                <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4'
                  />
                </svg>
                <span>Drag items to reorder and restructure</span>
              </div>
            </div>

            {isLoading ? (
              <div className='flex h-96 flex-col items-center justify-center space-y-4'>
                <div className='h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600'></div>
                <p className='text-sm text-gray-600'>Processing outline...</p>
                <Countdown duration={100} start={isLoading} />
              </div>
            ) : (
              <div className='pb-20' style={{ pointerEvents: 'auto' }}>
                <EditableTableOfContents
                  ref={outlineRef}
                  data={selectedOutline}
                  onChange={handleOutlineChange}
                  showPreview={false}
                  className='w-full'
                />
              </div>
            )}
          </div>

          {/* 右侧：目录预览 */}
          <div className='w-80 border-l border-gray-200 bg-gray-50 p-6'>
            <h3 className='mb-4 text-lg font-medium text-gray-800'>Table of Contents Preview</h3>
            <div className='space-y-2'>
              {selectedOutline.map((item, index) => (
                <div key={item.id} className='text-sm text-gray-700'>
                  <div className='flex items-center'>
                    <span className='mr-2'>{index + 1}</span>
                    <span>{item.title}</span>
                  </div>
                  {item.children && item.children.length > 0 && !item.isCollapsed && (
                    <div className='ml-4 mt-1 space-y-1'>
                      {item.children.map((child, childIndex) => (
                        <div key={child.id} className='flex items-center text-xs text-gray-600'>
                          <span className='mr-2'>
                            {index + 1}.{childIndex + 1}
                          </span>
                          <span>{child.title}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className='flex items-center justify-between border-t border-gray-200 px-6 py-4'>
          <button
            onClick={onBack}
            disabled={isSubmitting || isLoading}
            className='flex items-center space-x-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'>
            <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M10 19l-7-7m0 0l7-7m-7 7h18'
              />
            </svg>
            <span>Back to Requirements</span>
          </button>

          <button
            onClick={handleConfirm}
            disabled={isSubmitting || selectedOutline.length === 0 || isLoading}
            className='rounded-md px-6 py-2 text-white hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-50'
            style={{ backgroundColor: 'rgba(86, 97, 246, 1)' }}>
            {isSubmitting ? (
              <div className='flex items-center space-x-2'>
                <div className='h-4 w-4 animate-spin rounded-full border-b-2 border-white'></div>
                <span>Processing...</span>
              </div>
            ) : (
              <div className='flex items-center space-x-2'>
                <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M5 13l4 4L19 7'
                  />
                </svg>
                <span>Confirm & Generate Outline</span>
              </div>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default OutlineModal
