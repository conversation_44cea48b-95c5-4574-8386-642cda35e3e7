import React, { useEffect, useState } from 'react'

interface CountdownProps {
  duration?: number
  onEnd?: () => void
  start: boolean
  className?: string
}

const Countdown: React.FC<CountdownProps> = ({
  duration = 0,
  onEnd,
  start = false,
  className = '',
}) => {
  const [count, setCount] = useState<number>(duration)
  const [active, setActive] = useState<boolean>(start)

  useEffect(() => {
    if (!active || count <= 0) {
      if (count === 0 && active && onEnd) {
        onEnd()
      }
      return
    }

    const timer = setInterval(() => {
      setCount((prev) => prev - 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [active, count, onEnd])

  useEffect(() => {
    if (start) {
      setActive(true)
    }
  }, [start])

  return <span className={className}>{active ? `${count}s` : null}</span>
}

export default Countdown
