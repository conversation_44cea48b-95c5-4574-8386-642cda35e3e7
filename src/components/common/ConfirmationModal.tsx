// src/components/common/ConfirmationModal.tsx
import React, { useState } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { CheckIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  confirmationText: string
  onConfirm?: (editedText?: string) => void
  isLoading?: boolean
  showCountdown?: boolean
  countdownDuration?: number
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  confirmationText,
  onConfirm,
  isLoading = false,
  showCountdown = false,
  countdownDuration = 60,
}) => {
  const { t } = useTranslation(Namespace.GENERATOR)
  const [editableText, setEditableText] = useState(confirmationText)
  const [countdown, setCountdown] = useState(countdownDuration)

  // 当confirmationText更新时，同步更新editableText
  React.useEffect(() => {
    setEditableText(confirmationText)
  }, [confirmationText])

  // 倒计时逻辑
  React.useEffect(() => {
    if (showCountdown && isOpen && countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [showCountdown, isOpen, countdown])

  // 重置倒计时
  React.useEffect(() => {
    if (isOpen) {
      setCountdown(countdownDuration)
    }
  }, [isOpen, countdownDuration])

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm(editableText)
    }
  }

  if (!isOpen) return null

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center'>
      {/* 背景遮罩 */}
      <div
        className='absolute inset-0 bg-gray-500 bg-opacity-30 transition-opacity'
        onClick={onClose}
      />

      {/* 弹窗内容 */}
      <div className='relative mx-4 w-[500px] transform rounded-lg bg-white shadow-xl transition-all'>
        {/* 弹窗头部 */}
        <div className='flex h-[88px] flex-row items-start justify-start gap-2.5 rounded-t-lg border-b border-[rgba(228,229,236,1)] bg-[rgba(255,255,255,1)] px-6 py-[22px]'>
          <div className='flex-1'>
            <h3 className='font-roboto text-[18px] font-semibold leading-[28px] text-[rgba(9,9,11,1)]'>
              {t('confirmationModal.title')}
            </h3>
            <p className='mt-1 font-roboto text-[12px] font-normal leading-[14px] text-[rgba(182,190,211,1)]'>
              {t('confirmationModal.subtitle')}
            </p>
          </div>
          <button onClick={onClose} className='text-gray-400 transition-colors hover:text-gray-600'>
            <XMarkIcon className='h-6 w-6' />
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className='flex h-[184px] flex-col items-start justify-start gap-7 bg-[rgba(255,255,255,1)] px-10 py-5'>
          {/* AI确认的问题 - 可编辑 */}
          <div className='w-full'>
            <textarea
              value={editableText}
              onChange={(e) => setEditableText(e.target.value)}
              className='w-full resize-none border-none bg-transparent font-roboto text-[16px] font-normal leading-[24px] text-[rgba(66,73,98,1)] outline-none focus:ring-0'
              rows={6}
              placeholder={t('confirmationModal.placeholder')}
            />
          </div>
        </div>

        {/* 弹窗底部按钮 */}
        <div className='flex h-[80px] flex-row items-start justify-end gap-2 rounded-b-lg border-t border-gray-200 bg-[rgba(255,255,255,1)] px-6 py-5'>
          <div className='flex space-x-2'>
            <button
              onClick={onClose}
              disabled={isLoading}
              className='flex h-[40px] w-[82px] flex-row items-center justify-center gap-1 rounded-[6px] bg-[rgba(235,238,245,1)] px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'>
              {t('confirmationModal.cancel')}
            </button>
            <button
              onClick={handleConfirm}
              disabled={isLoading}
              className='flex h-[40px] w-[159px] flex-row items-center justify-center gap-1 rounded-[6px] bg-[rgba(86,97,246,1)] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'>
              {isLoading ? (
                <>
                  <svg className='h-4 w-4 animate-spin' fill='none' viewBox='0 0 24 24'>
                    <circle
                      className='opacity-25'
                      cx='12'
                      cy='12'
                      r='10'
                      stroke='currentColor'
                      strokeWidth='4'
                    />
                    <path
                      className='opacity-75'
                      fill='currentColor'
                      d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                    />
                  </svg>
                  {t('confirmationModal.processing')}
                </>
              ) : showCountdown && countdown > 0 ? (
                <>
                  <CheckIcon className='h-4 w-4' />
                  {t('confirmationModal.confirm')} ({countdown}s)
                </>
              ) : (
                <>
                  <CheckIcon className='h-4 w-4' />
                  {t('confirmationModal.confirm')}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConfirmationModal
