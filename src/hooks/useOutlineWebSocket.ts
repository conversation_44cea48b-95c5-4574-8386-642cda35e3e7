import { useCallback, useRef, useState } from 'react'
import useWebSocketWithReconnection from '@/pages/deep-explore/socket'
import type { TocItem } from '@/components/common/EditableTableOfContentes'

// 简单的ID生成函数
const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

interface UseOutlineWebSocketProps {
  onOutlineReceived?: (outlineData: TocItem[]) => void
  onOutlineModalOpen?: () => void
  onConfirmLoadingChange?: (loading: boolean) => void
  onModalClose?: () => void
}

export const useOutlineWebSocket = ({
  onOutlineReceived,
  onOutlineModalOpen,
  onConfirmLoadingChange,
  onModalClose,
}: UseOutlineWebSocketProps) => {
  const taskIdRef = useRef<string>('')
  const [isConnected, setIsConnected] = useState(false)

  // WebSocket消息处理
  const handleSocketMessage = useCallback(
    (message: any) => {
      console.log('收到WebSocket消息:', message)

      // 检查是否是确认相关的消息 - 支持多种格式
      const isConfirmMessage =
        (message.type === 'confirm' && message.task_id === taskIdRef.current) ||
        (message.processId && message.processId === taskIdRef.current) ||
        (message.taskId && message.taskId === taskIdRef.current)

      if (isConfirmMessage) {
        console.log('匹配到当前任务的WebSocket消息:', {
          messageType: message.type,
          taskId: message.task_id || message.processId || message.taskId,
          currentTaskId: taskIdRef.current,
        })

        const { data } = message

        // 检查是否有大纲数据
        console.log('消息数据:', data)

        // 检查大纲数据格式
        let outlineData = null

        // 格式: data.message['报告大纲']
        if (data && data.message && data.message['报告大纲']) {
          outlineData = data.message['报告大纲']
          console.log('找到大纲数据:', outlineData)
        }

        if (outlineData && Array.isArray(outlineData) && outlineData.length > 0) {
          console.log('大纲数据类型:', typeof outlineData)
          console.log('大纲数据长度:', outlineData.length)
          console.log('第一个大纲项:', outlineData[0])

          const processOutlineData = (items: any[], level = 0): TocItem[] => {
            return items.map((item, index) => {
              console.log(`处理大纲项 ${index}:`, item)
              return {
                ...item,
                id: item.id || generateId(),
                level,
                children: item.children ? processOutlineData(item.children, level + 1) : [],
              }
            })
          }

          const processedOutline = processOutlineData(outlineData)
          console.log('处理后的大纲数据:', processedOutline)

          // 调用回调函数
          onOutlineReceived?.(processedOutline)
          onOutlineModalOpen?.()
          onConfirmLoadingChange?.(false)
          onModalClose?.()
        } else {
          console.log('消息中没有找到有效的大纲数据')
        }
      } else {
        console.log('WebSocket消息不匹配:', {
          messageType: message.type,
          messageTaskId: message.task_id,
          messageProcessId: message.processId,
          currentTaskId: taskIdRef.current,
        })
      }
    },
    [onOutlineReceived, onOutlineModalOpen, onConfirmLoadingChange, onModalClose],
  )

  // WebSocket连接
  const { startWebSocket, isConnected: wsConnected } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })

  // 更新连接状态
  const updateConnectionStatus = useCallback((connected: boolean) => {
    setIsConnected(connected)
  }, [])

  // 设置任务ID
  const setTaskId = useCallback((taskId: string) => {
    taskIdRef.current = taskId
  }, [])

  // 获取当前任务ID
  const getTaskId = useCallback(() => {
    return taskIdRef.current
  }, [])

  return {
    startWebSocket,
    isConnected: wsConnected,
    setTaskId,
    getTaskId,
    updateConnectionStatus,
  }
}
