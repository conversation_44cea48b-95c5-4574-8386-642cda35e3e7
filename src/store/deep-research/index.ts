/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  InitRequirementRequest,
  StartTaskRequest,
  getTaskStatusApi,
  initRequirementApi,
  startTaskApi,
} from '@/api/deep-research'
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { v4 as uuidv4 } from 'uuid'
export type OutlineItem = {
  title: string
  children?: OutlineItem[]
  description: string
  id?: string
  level?: number
  chapterIndex?: string
}

export type OutlineType = {
  原始问题: string
  合规判定: boolean
  报告大纲: OutlineItem[]
  问题确认: string
  拒绝原因?: string
}

const processOutlineData = (
  items: OutlineItem[],
  level = 0,
  parentSequence: string = '',
): OutlineItem[] => {
  return items.map((item, index) => {
    // 生成当前项的序号
    const currentSequence = parentSequence ? `${parentSequence}.${index + 1}` : `${index + 1}`

    return {
      ...item,
      id: item.id || uuidv4(),
      level,
      chapterIndex: currentSequence, // 添加序号
      children: item.children ? processOutlineData(item.children, level + 1, currentSequence) : [],
    }
  })
}

interface DeepResearchStore {
  getRequirement: (question: string, user_language: string) => Promise<InitRequirementRequest>
  requirement: InitRequirementRequest['data']['requirement']
  taskId: string
  outline: OutlineType // 输出的大纲
  setOutline: (outline: OutlineType) => void
  startTask: (data: {
    messages: {
      content: string
      role: 'user'
    }[]
    outline: any
    question: string
    requirement: string
    task_id: string
    user_language: string
  }) => Promise<any>
  taskStatus: StartTaskRequest
  getTaskStatus: (taskId: string) => Promise<any>
  setRequirement: (requirement: InitRequirementRequest['data']['requirement']) => void
}
export const useDeepResearchStore = create<DeepResearchStore>()(
  immer((set) => ({
    requirement: {} as InitRequirementRequest['data']['requirement'],
    taskId: '',
    outline: {} as OutlineType,
    taskStatus: {} as StartTaskRequest,
    // 获取输入问题的扩写
    getRequirement: async (question: string, user_language: string) => {
      try {
        const res = await initRequirementApi({ question, user_language })
        set({ requirement: res.data.requirement, taskId: res.data.task_id })
        return res
      } catch (error) {
        console.error('initRequirement', error)
        throw error
      }
    },
    setRequirement: (requirement) => {
      set({ requirement })
    },
    setOutline: (outline) => {
      outline.报告大纲 = processOutlineData(outline.报告大纲)
      set({ outline })
    },
    startTask: async (data: {
      messages: {
        content: string
        role: 'user'
      }[]
      outline: OutlineType
      question: string
      requirement: string
      task_id: string
      user_language: string
    }) => {
      try {
        const res = await startTaskApi(data)
        return res
      } catch (error) {
        console.error('startTask', error)
        throw error
      }
    },
    getTaskStatus: async (taskId: string) => {
      try {
        const res = await getTaskStatusApi({ task_id: taskId })

        set({ taskStatus: res.data })
        return res
      } catch (error) {
        console.error('getTaskStatus', error)
        throw error
      }
    },
  })),
)
