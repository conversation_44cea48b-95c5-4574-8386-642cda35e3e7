import { create } from 'zustand'
import { SubscriptionEnum } from '@/types'
import { setLangToCookies } from '@/lib/cookie'

export interface User {
  userId: string
  name: string
  email: string
  picture: string
  setting: {
    language: string
    isAutoAgent: boolean
    disableToolbar?: boolean
  }
  usage: {
    freeQuota: number
    freeUsage: number
    paidQuota: number
    paidUsage: number
    totalQuota: number
    totalUsage: number
  }
  currentPlan: SubscriptionEnum
}

interface UserStore {
  user: User | undefined | null // User can be undefined or null
  updateUser: (updatedUser: User) => void
  clearUser: () => void
}

export const useUserStore = create<UserStore>((set) => ({
  user: null, // Initialize as null
  updateUser: (updatedUser) => {
    set({ user: updatedUser }) // Directly update the user
    setLangToCookies(updatedUser.setting.language)
  },
  clearUser: () => set({ user: null }), // Clear user info
}))
