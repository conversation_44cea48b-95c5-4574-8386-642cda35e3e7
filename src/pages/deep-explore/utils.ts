import { ReferenceItem } from '@/api/getSession'
import { ImageStatusEnum } from '@/api/imgs/imageRunning'
import { PDFStatusEnum } from '@/api/pdfs/getPdf'
import { ProcessStatusEnum } from '@/types'

export type SocketMessages = FileMessage | PdfMessage | PdfProcessMessage

export interface SocketMessageData {
  summaryStream: string
  status: ProcessStatusEnum
  needSearch?: boolean
  reasoningStream?: string
  referenceList?: ReferenceItem[]
  downloadPdf?: boolean
  imageStatus?: ImageStatusEnum
}

export interface PdfMessage {
  type: 'pdf'
  pdfId: string
  data: {
    status: PDFStatusEnum
    s3Url?: string
    title?: string
  }
}

export interface FileMessage {
  type: 'file'
  fileId: string
  data: {
    status: PDFStatusEnum
    s3Url?: string
    title?: string
  }
}

export interface GeneralSocketMessage {
  type: 'process'
  processId: string
  data: SocketMessageData
}

export enum PdfProcessStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  OUTPUTING = 'OUTPUTING',
  FINISH = 'FINISH',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export interface PdfProcessMessage {
  type: 'pdfProcess'
  pdfProcessId: string
  data: {
    message: string
    status: PdfProcessStatusEnum
    error?: string
  }
}
