import Layout from '@/components/business/layout/Layout'
import { FileIndex } from './components/file-index'
import { FileTop } from './components/file-top'
const FileDisk = () => {
  // useEffect(() => {
  //   Tracking.trackPageView({ eventName: 'VISIT_FILE_EXPLORE_PAGE' })
  // }, [])
  return (
    <Layout tabName='explore' showSideBar={false}>
      <div
        className='h-screen-minus-header bg-card pt-5'
        asm-tracking='TEST_VISIT_FILE_EXPLORE_PAGE:VIEW'>
        <div className='w-full'>
          <FileTop />
          <FileIndex />
        </div>
      </div>
    </Layout>
  )
}
export default FileDisk
