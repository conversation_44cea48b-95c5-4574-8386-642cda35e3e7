import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { listFileApi, getFileTotalSizeApi, deleteFileApi } from '@/api/pdfs/uploadFiles'

interface FileStore {
  search: {
    type?: 'PDF' | 'Image'
    sort: 'DESC' | 'ASC'
    pageSize: number
    lastPageKey: string | null
  }
  setSearch: (key: keyof FileStore['search'], value: FileStore['search'][key]) => void
  selectedAll: 'indeterminate' | boolean
  toggleSelectAll: (checked: boolean) => void
  toggleSelectItem: (checked: boolean) => void
  setLoading: (load: boolean) => void
  fileData: typeof apiJson
  selectedIds: Array<string> // 已选的id数组
}
export const useFile = create<FileStore>()(
  immer((set, get) => ({
    // 文档搜索条件 ASC DESC
    search: {
      sort: 'DESC',
      pageSize: 30,
      lastPageKey: null,
    },
    isLoading: true,
    setLoading: (load) => {
      set({
        isLoading: load,
      })
    },
    setSearch: (search) => {
      set({
        search: {
          ...get().search,
          ...search,
        },
      })
      get().getFileList()
    },
    // 文档全选与半选
    selectedAll: '',
    toggleSelectAll: (checked) => {
      set({
        selectedAll: checked,
        selectedIds: checked ? get().fileData.map((item) => item.fileId) : [],
        fileData: get().fileData.map((item) => ({ ...item, checked })),
      })
    },
    toggleSelectItem: (checked, item) => {
      // 更改当前项
      set({
        fileData: get().fileData.map((f) => {
          if (f.fileId === item.fileId) {
            return { ...f, checked }
          }
          return f
        }),
      })
      // 更改选中的项
      set({
        selectedIds: get()
          .fileData.filter((f) => f.checked)
          .map((i) => i.fileId),
      })
      // 全选状态
      const selectedIdsLength = get().selectedIds.length
      const allLength = get().fileData.length
      set({
        selectedAll:
          selectedIdsLength === 0
            ? false
            : selectedIdsLength === allLength
              ? true
              : 'indeterminate',
      })
    },
    // 接口数据
    fileData: [],
    totalPages: 0,
    // 获取列表数据
    getFileList: async (isfirst) => {
      const { sort, pageSize, lastPageKey } = get().search
      const res = await listFileApi({
        sort,
        pageSize,
        lastPageKey: isfirst ? null : lastPageKey,
      })
      set({
        fileData: res.files,
        isLoading: false,
        selectedAll: false,
        selectedIds: [],
        totalPages: res.total,
        search: {
          ...get().search,
          lastPageKey: res.pageKey,
        },
      })
      await get().getTotalSize()
    },
    // 已选的id
    selectedIds: [],
    // 获取文件总大小
    totalSize: 0,
    maxSize: 0,
    getTotalSize: async () => {
      const res = await getFileTotalSizeApi()
      set({
        totalSize: res.size,
        maxSize: res.maxSize,
      })
    },
    // 删除文档
    deleteFiles: async ({ cb }) => {
      const { selectedIds } = get()
      if (selectedIds.length === 0) {
        return
      }
      await deleteFileApi({
        fileIds: selectedIds,
      })
      set({
        selectedIds: [],
        selectedAll: '',
        isLoading: true,
      })
      cb && cb()
      await get().getFileList(true)
    },
  })),
)
