export enum FileTypeEnum {
  PDF = 'PDF',
  IMG = 'IMG',
  ALL = 'ALL',
}

export const FileTypeList: {
  value: FileTypeEnum
  key: string
}[] = [
  {
    value: FileTypeEnum.ALL,
    key: 'all',
  },
  {
    value: FileTypeEnum.PDF,
    key: 'PDF',
  },
  {
    value: FileTypeEnum.IMG,
    key: 'Image',
  },
]

export const FileSortList = [
  {
    value: 'sequence',
    key: 'DESC',
  },
  {
    value: 'reverse',
    key: 'ASC',
  },
]

// 单位转化 MB ---> GB
export const unitConversion = (size: number): string => {
  return `${(size / 1024).toFixed(2)}G`
}
