import { Button } from '@/components/ui/button'
import { TextEnum, Text } from '@/components/business/text'
import { TrashIcon, BarsArrowUpIcon } from '@heroicons/react/24/outline'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { DropdownMenus } from './DropdownMenu'
import { FileSortList } from '../../const'
import { useState } from 'react'
import { useFile } from '../../store/useFile'
import ModalDelete from './ModalDelete'
export const RetrievalOperation = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const [visibleSort, setVisibleSort] = useState(false)
  const [visibleDelete, setVisibleDelete] = useState(false)
  const search = useFile((s) => s.search)
  const setSearch = useFile((s) => s.setSearch)
  const selectedIds = useFile((s) => s.selectedIds)
  const deleteFiles = useFile((s) => s.deleteFiles)
  const onDeleteFile = () => {
    setVisibleDelete(true)
  }
  const onConfirm = () => {
    deleteFiles({
      cb: () => setVisibleDelete(false),
    })
  }
  return (
    <div>
      <ModalDelete
        open={visibleDelete}
        onClose={() => setVisibleDelete(false)}
        onConfirm={onConfirm}
      />
      <Button
        className='mr-4 rounded-sm'
        variant='common'
        size='sm'
        onClick={onDeleteFile}
        disabled={!selectedIds.length}>
        <TrashIcon className='mr-1.5 h-5 w-5' />
        <Text type={TextEnum.Body_medium} className='translate-y-[1px]'>
          {t('sideMore.delete')}
        </Text>
      </Button>
      {/* <DropdownMenus
        className='mx-4'
        list={FileTypeList}
        open={visibleType}
        currentKey={search.type}
        onInteractOutside={() => setVisibleType(false)}
        onClickDropItem={(item) => {
          setSearch('type', item.key)
          setVisibleType(false)
        }}>
        <Button
          className='rounded-sm px-2 focus-visible:ring-0 focus-visible:ring-offset-0'
          variant='common'
          size='sm'
          onClick={() => setVisibleType(!visibleType)}>
          <FunnelIcon className='h-5 w-5' />
        </Button>
      </DropdownMenus> */}
      <DropdownMenus
        list={FileSortList}
        open={visibleSort}
        currentKey={search.sort}
        onInteractOutside={() => setVisibleSort(false)}
        onClickDropItem={(item) => {
          setSearch({
            sort: item.key,
          })
          setVisibleSort(false)
        }}>
        <Button
          className='rounded-sm px-2 focus-visible:ring-0 focus-visible:ring-offset-0'
          variant='common'
          onClick={() => setVisibleSort(!visibleSort)}
          size='sm'>
          <BarsArrowUpIcon className='h-5 w-5' />
        </Button>
      </DropdownMenus>
    </div>
  )
}
