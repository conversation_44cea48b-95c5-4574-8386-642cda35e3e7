import { batchDownloadPdf } from '@/api/pdfs/batchDownloadPdf'
import { deletePdf } from '@/api/pdfs/deletePdf'
import {
  extractProcessPdfsApi,
  getPdfs,
  ParentSessionType,
  PDFItemType,
  PdfPermissionEnum,
  PDFStatusEnum,
} from '@/api/pdfs/getPdf'
import { pdfStartIndex } from '@/api/pdfs/pdfStartIndex'
import Layout from '@/components/business/layout/Layout'
import { Namespace } from '@/i18n'
import { useLocalStorage } from '@/lib/hooks/useLocalStorage'
import { PoTypeEnum } from '@/types'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import DeleteModal from './components/DeleteModal'
import { ContentLayout } from './components/Layout'
import { PdfList } from './components/PdfList'
import useWebSocketWithReconnection from './socket'
import { PdfProcessMessage, SocketMessages } from './utils'
// import { uploadFiles } from '@/api/pdfs/uploadFiles'

const DeepExplore = () => {
  const router = useRouter()
  const { processId, pdfId } = router.query

  const { t } = useTranslation([Namespace.DEEPEXPLORE, Namespace.GLOBAL])

  // const childRef = useRef<PdfListComponentHandles | null>(null)

  const [, setIsCollapseLsState] = useLocalStorage('navigationCollapseState')

  const [pdfs, setPdfs] = useState<PDFItemType[] | undefined>(undefined)
  const [selectedPdf, setSelectedPdf] = useState<PDFItemType | undefined>(undefined)

  const [toBeRemoved, setToBeRemoved] = useState<PDFItemType | undefined>(undefined)
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false)

  const [parentSession, setParentSession] = useState<ParentSessionType>({
    sessionId: '',
    title: '',
    poType: PoTypeEnum.GENERAL,
  })

  // 当前正在聊天的 processId
  const pdfProcessId = useRef('')
  const [chatMessageStream, setChatMessageStream] = useState<PdfProcessMessage | undefined>(
    undefined,
  )

  // 定义消息处理函数
  const handleSocketMessage = (message: SocketMessages) => {
    if (message.type === 'pdfProcess') {
      processChatMessages(message)
    }
    if (message.type === 'pdf') {
      setPdfs((prevPdfs) => {
        if (!prevPdfs) return []
        const updatedPdfs = prevPdfs.map((item) => {
          if (item.pdfId === message.pdfId) {
            const messageData = message.data
            if (item.status === PDFStatusEnum.INDEX_FAILED) {
              messageData.status = PDFStatusEnum.INDEX_FAILED
            }
            return {
              ...item,
              ...messageData,
            }
          }
          return item
        })

        return JSON.stringify(updatedPdfs) === JSON.stringify(prevPdfs) ? prevPdfs : updatedPdfs
      })

      if (selectedPdfRef.current?.pdfId === message.pdfId) {
        setSelectedPdf((prevPdfs) => {
          if (!prevPdfs) return undefined

          const updatedPdfs = {
            ...prevPdfs,
            ...message.data,
          }

          return JSON.stringify(updatedPdfs) === JSON.stringify(prevPdfs) ? prevPdfs : updatedPdfs
        })
        // 如果推送了 OCR_SUCCESS 需要主动触发 index
        if (message.data.status === PDFStatusEnum.OCR_SUCCESS) {
          try {
            pdfStartIndex({
              pdfId: message.pdfId,
            })
          } catch (error) {
            console.log('')
          }
        }
      }
    }
  }

  // 创建连接
  const { socket, isConnected, startWebSocket } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })

  // 页面初始化
  useEffect(() => {
    if (processId) {
      // 获取 PDF List 设置当前选中的值
      getPdfs({
        processId: processId as string,
      }).then((data) => {
        if (data.pdfList) {
          setPdfs(data.pdfList)
          const res = data.pdfList.find((item) => item.pdfId === pdfId)
          res ? setSelectedPdf(res) : setSelectedPdf(JSON.parse(JSON.stringify(data.pdfList[0])))
        }
        if (data.parentSession) {
          setParentSession(data.parentSession)
        }
        // 推动PDF状态
        const pdfList = data.pdfList
        if (
          pdfList.length &&
          pdfList.some(
            (p) =>
              p.pdfPermission === PdfPermissionEnum.NORMAL &&
              p.status === PDFStatusEnum.DOWNLOAD_SUCCESS,
          )
        ) {
          extractProcessPdfsApi({
            processId: processId as string,
          })
        }
        // 触发批量下载
        if (
          data.pdfList.length > 0 &&
          data.pdfList.some((item) => item.status === PDFStatusEnum.INIT)
        ) {
          batchDownloadPdf({
            processId: processId as string,
          })
        }
      })
    }
  }, [processId, pdfId])

  // 使用 useRef 存储 selectedPdf 的最新值，给 handleSocketMessage
  const selectedPdfRef = useRef(selectedPdf)

  useEffect(() => {
    selectedPdfRef.current = selectedPdf
    try {
      // PDF 选中后 触发向量化
      if (selectedPdf?.pdfId && selectedPdf.status === PDFStatusEnum.OCR_SUCCESS) {
        pdfStartIndex({
          pdfId: selectedPdf.pdfId,
        })
      }
      // 切换PDF 清空消息流, 并且保证有连接
      if (!socket || !isConnected) {
        startWebSocket()
      }
      setChatMessageStream(undefined)
    } catch (error) {
      console.log('extract result,', error)
    }
  }, [selectedPdf?.status, selectedPdf?.pdfId])

  useEffect(() => {
    setIsCollapseLsState('false')
  }, [])

  const processChatMessages = (message: SocketMessages) => {
    if (message.type === 'pdfProcess' && message.pdfProcessId === pdfProcessId.current) {
      setChatMessageStream({
        type: 'pdfProcess',
        pdfProcessId: message.pdfProcessId,
        data: message.data,
      })
    }
  }

  const handleChangePdfProcessId = (id: string) => {
    pdfProcessId.current = id
  }

  const handleChangePdf = (item: PDFItemType) => {
    setSelectedPdf(item)
  }

  const handleRemovePdf = async (item?: PDFItemType) => {
    if (item?.pdfId) {
      setToBeRemoved(item)
      setShowDeleteModal(true)
    }
  }

  const handleConfirmDeletePdf = async () => {
    if (toBeRemoved?.pdfId) {
      try {
        const res = await deletePdf({
          pdfId: toBeRemoved?.pdfId,
        })
        if (res && res.result === 'ok') {
          setShowDeleteModal(false)
          getPdfs({
            processId: processId as string,
          }).then((data) => {
            if (data.pdfList) {
              setPdfs(data.pdfList)
              const res = data.pdfList.find((item) => item.pdfId === pdfId)
              res ? setSelectedPdf(res) : setSelectedPdf(data.pdfList[0])
            }
            if (data.parentSession) {
              setParentSession(data.parentSession)
            }
          })
        } else {
          toast(t('global:error.systemError'), {
            duration: 3000,
          })
        }
      } catch (error) {
        toast(t('global:error.systemError'), {
          duration: 3000,
        })
      }
    }
  }

  const checkSocket = () => {
    if (isConnected) {
      return true
    } else {
      startWebSocket()
      return isConnected
    }
  }

  return (
    <>
      <div className='min-w-fit'>
        <Layout
          sideBarContent={
            <PdfList
              pdfs={pdfs}
              selectedPdf={selectedPdf}
              onChangePdf={handleChangePdf}
              onDelete={handleRemovePdf}
            />
          }
          tabName='explore'>
          <ContentLayout
            selectedPdf={selectedPdf}
            parentSession={parentSession}
            onDelete={handleRemovePdf}
            onChangePdfProcessId={handleChangePdfProcessId}
            chatMessageStream={chatMessageStream}
            checkSocket={checkSocket}
          />
        </Layout>
      </div>
      <DeleteModal
        open={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false)
        }}
        onConfirm={handleConfirmDeletePdf}
      />
    </>
  )
}

export default DeepExplore
