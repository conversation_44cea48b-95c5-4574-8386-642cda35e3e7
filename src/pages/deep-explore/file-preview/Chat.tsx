import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { ChatTag } from '../components/ChatTag'
import EmptyConent from '@/components/business/recommend/img/EmptyContent'
import { Textinput } from '@/components/business/text-input'

export const Chat: React.FC<> = () => {
  const { t } = useTranslation([Namespace.DEEPEXPLORE, Namespace.GLOBAL])

  return (
    <div className='flex h-[100%] flex-col justify-between'>
      <EmptyConent></EmptyConent>

      {/* <div className='hide-scrollbar flex-1 overflow-scroll'></div> */}
      <div className='shrink-0'>
        <div className='mb-2 flex flex-row'>
          <ChatTag
            text={t('chat.summarizePdf')}
            // disabled={showStopButton || querying}
            onClick={() => {
              // handleClickSummarizeTag(QuickPDFInquiryEnum.DESCRIBE)
            }}
          />
        </div>

        <Textinput
          withIcon={true}
          placeholder={t('chat.inputPlacehoder')}
          maxLength={300}
          className='flex-1 shrink-0'
          // onSearch={(value: string) => {
          //   handleSendQuery({
          //     query: value,
          //   })
          // }}
          // onEnter={(value: string) => {
          //   handleSendQuery({
          //     query: value,
          //   })
          // }}
          // showStop={showStopButton}
          // disabled={}
          onStop={() => {
            // setOpenStopModal(true)
          }}
          // value={inputValue}
          // onChange={handleChange}
        />
      </div>
    </div>
  )
}
