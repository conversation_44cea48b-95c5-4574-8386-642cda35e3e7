import Layout from '@/components/business/layout/Layout'
import { Header } from './Header'
import { useRouter } from 'next/router'
import { Preview, PreviewProps } from './Preview'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'
import { getDetailsApi } from '@/api/pdfs/uploadFiles'

const FilePreview = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()
  const [fileItem, setFileItem] = useState<PreviewProps>()

  const getFile = async (fileId: string) => {
    const res = await getDetailsApi({ fileId })
    setFileItem(res)
  }

  useEffect(() => {
    router.query.fileId && getFile(router.query.fileId as string)
  }, [router.query.fileId])
  return (
    <Layout tabName='explore' showSideBar={false}>
      {fileItem && (
        <>
          <Header title={t('header.tab3')} url={fileItem.s3Url} />
          <Preview file={fileItem} />
        </>
      )}
    </Layout>
  )
}
export default FilePreview
