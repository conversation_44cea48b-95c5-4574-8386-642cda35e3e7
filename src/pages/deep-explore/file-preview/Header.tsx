import { TextEnum, Text } from '@/components/business/text'
import { smartiesRoutes } from '@/routers'
import { ArrowTopRightOnSquareIcon, SlashIcon } from '@heroicons/react/24/outline'
import router from 'next/router'

interface HeaderProps {
  title?: string
  url?: string
}

export const Header: React.FC<HeaderProps> = ({ title, url }) => {
  const handleClickUrl = () => {
    window.open(url, '_blank')
  }

  const handleOpenSession = () => {
    router.push({
      pathname: smartiesRoutes.deepExplore.fileDisk,
    })
  }
  return (
    <div className='flex h-11 w-full items-center gap-2.5 bg-card pl-4'>
      <Text
        onClick={handleOpenSession}
        type={TextEnum.Body_medium}
        className='max-w-[112px] cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap text-secondary-black-3'>
        {title}
      </Text>
      <SlashIcon className='h-4 shrink-0 text-secondary-black-3' />
      <Text
        onClick={handleClickUrl}
        type={TextEnum.Body_medium}
        className='max-w-[376px] cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap'>
        {url}
      </Text>
      <ArrowTopRightOnSquareIcon
        className='h-4 w-4 shrink-0 cursor-pointer text-secondary-foreground'
        onClick={handleClickUrl}
      />
    </div>
  )
}
