import { PDFItemType, PDFStatusEnum, PdfPermissionEnum } from '@/api/pdfs/getPdf'
import { pdfStartIndex } from '@/api/pdfs/pdfStartIndex'
import ImgChat from '@/components/business/recommend/img/ImgChat'
import { Namespace } from '@/i18n'
import { MediaPreview } from '@/pages/report/components/source-list/media/MediaPreview'
import { PdfPreviews } from '@/pages/report/components/source-list/pdf/PdfPreview'
import { ArrowPathIcon } from '@heroicons/react/24/outline'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import DocChat from '../components/DocChat'
import useWebSocketWithReconnection from '../socket'
import { PdfProcessMessage, SocketMessages } from '../utils'
export type PreviewProps = {
  mediaId?: string
  fileId?: string
  parentSession: PdfPermissionEnum
  fileType: 'PDF' | 'IMAGE'
  url: string // 资源url
} & PDFItemType

export const Preview: React.FC<{ file: PreviewProps }> = ({ file }) => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const pdfProcessId = useRef('')
  const childRef = useRef<{ updateInputPage: (num: number) => void }>(null)
  const currentFileId = useRef<string>()

  const [chatMessageStream, setChatMessageStream] = useState<PdfProcessMessage>()
  const [selectedFile, setSelectedFile] = useState<PDFItemType>()
  const isChatIng = useRef(false)

  const onChangePdfProcessId = (id: string) => {
    pdfProcessId.current = id
  }
  const processChatMessages = (message: SocketMessages) => {
    isChatIng.current = true
    if (message.type === 'pdfProcess' && message.pdfProcessId === pdfProcessId.current) {
      setChatMessageStream({
        type: 'pdfProcess',
        pdfProcessId: message.pdfProcessId,
        data: message.data,
      })
    }
  }
  const handleClickedPdfNumber = ({ pageNumber }: { pageNumber: number }) => {
    if (childRef.current) {
      childRef.current.updateInputPage(pageNumber)
    }
  }
  const handleSocketMessage = (message: SocketMessages) => {
    if (message.type === 'pdfProcess') {
      processChatMessages(message)
    }
    if (message.type === 'file') {
      if (currentFileId.current === message.fileId) {
        setSelectedFile((prevFile) => {
          if (!prevFile) {
            return
          }
          const messageData = message.data
          if (prevFile.status === PDFStatusEnum.INDEX_FAILED) {
            messageData.status = PDFStatusEnum.INDEX_FAILED
          }
          const updatedFile = {
            ...prevFile,
            ...messageData,
          }
          return JSON.stringify(updatedFile) === JSON.stringify(prevFile) ? prevFile : updatedFile
        })
      }
    }
    if (message?.data?.status === 'FINISH' || message?.data?.status === 'FAILED') {
      pdfProcessId.current = ''
      isChatIng.current = false
    }
  }

  const onRefresh = () => {
    if (isChatIng.current) {
      toast(
        <div
          onClick={() => {
            window.location.reload()
          }}
          className='flex items-center'>
          <ArrowPathIcon className='mr-2 h-5 w-5 cursor-pointer'></ArrowPathIcon>
          {t('error.netError')}
        </div>,
        {
          duration: 3000,
        },
      )
    }
  }
  // 创建连接
  const { socket, isConnected, startWebSocket, retryFailed, closeWebSocket } =
    useWebSocketWithReconnection({
      onMessage: handleSocketMessage,
      onRetryRefresh: onRefresh,
    })
  useEffect(() => {
    if (retryFailed) {
      onRefresh()
    }
  }, [retryFailed])
  // 切换文件时，创建 websocket
  useEffect(() => {
    if (file.fileType === 'IMAGE' && socket && isConnected) {
      closeWebSocket()
      return
    }
    setChatMessageStream(undefined)
    setSelectedFile(file)
    currentFileId.current = file.fileId
    pdfProcessId.current = ''
    if (
      file.status === PDFStatusEnum.DOWNLOAD_SUCCESS &&
      file.parentSession === PdfPermissionEnum.NORMAL
    ) {
      try {
        pdfStartIndex({
          pdfId: file.fileId as string,
        })
      } catch (error) {
        console.log('')
      }
    }
  }, [file.fileId, socket, isConnected])

  const checkSocket = () => {
    if (isConnected) {
      return true
    } else {
      startWebSocket()
      return isConnected
    }
  }
  useEffect(() => {
    const handleOffline = () => {
      onRefresh()
    }
    window.addEventListener('offline', handleOffline)
    return () => {
      window.removeEventListener('offline', handleOffline)
    }
  }, [])
  const typeComponent = {
    PDF: (
      <PdfPreviews
        isShowChat={false}
        header2Class='h-screen-minus-2-header'
        header3Class='h-screen-minus-3-header'
        ref={childRef}
        pdf={file}>
        <div className='h-screen-minus-2-header w-full p-4'>
          <DocChat
            selectedPdf={{
              ...(selectedFile as PDFItemType),
              pdfId: file.fileId as string,
            }}
            saveToReport={true}
            onChangePdfProcessId={onChangePdfProcessId}
            chatMessageStream={chatMessageStream}
            onChangePdfNumber={handleClickedPdfNumber}
            checkSocket={checkSocket}
            source='file'
          />
        </div>
      </PdfPreviews>
    ),
    IMAGE: (
      <MediaPreview
        isShowChat={false}
        width='w-[71%] min-w-[600px]'
        height='h-screen-minus-2-header'
        media={{
          url: file.s3Url,
        }}>
        <div className='h-screen-minus-2-header w-[29%] min-w-[420px] p-4'>
          <ImgChat
            saveToReport={true}
            img={{
              mediaId: file.fileId as string,
              ...selectedFile,
            }}
            source='file'
          />
        </div>
      </MediaPreview>
    ),
  }
  return <>{typeComponent[file.fileType]}</>
}
