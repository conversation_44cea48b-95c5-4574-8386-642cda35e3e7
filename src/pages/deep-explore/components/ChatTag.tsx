import { SparklesIcon } from '@heroicons/react/24/outline'
import { TextEnum, Text } from '@/components/business/text/'
import clsx from 'clsx'

export const ChatTag = ({
  text,
  onClick,
  className,
  disabled,
}: {
  text: string
  onClick: () => void
  className?: string | string[]
  disabled: boolean
}) => {
  return (
    <div
      className={clsx(
        'flex-v-center cursor-pointer rounded-md border border-border bg-card px-3 py-2 hover:bg-secondary-hover',
        className,
      )}
      onClick={() => {
        if (!disabled) {
          onClick()
        }
      }}>
      <SparklesIcon className='mr-1 h-4 w-4 shrink-0' />
      <Text type={TextEnum.Body_medium} className='overflow-hidden text-ellipsis whitespace-nowrap'>
        {text}
      </Text>
    </div>
  )
}
