// import { TextEnum, Text } from '@/components/business/text'
// import { Button } from '@/components/ui/button'
// import { Namespace } from '@/i18n'
// import { ArrowUpTrayIcon } from '@heroicons/react/24/outline'
// import { useTranslation } from 'react-i18next'
import { PdfCard } from './PdfCard'
import { PDFItemType } from '@/api/pdfs/getPdf'
// import { ImportFilesModal } from '../../../components/business/upload/ImportFilesModal'
// import { useImperativeHandle } from 'react'
// import { uploadUrl } from '@/api/pdfs/uploadUrl'
// import { toast } from 'sonner'
import React from 'react'

interface PDFListProps {
  pdfs?: PDFItemType[]
  selectedPdf?: PDFItemType
  onChangePdf: (item: PDFItemType) => void
  // processId?: string
  // onPasteUrl?: (item: PDFItemType) => void
  onDelete?: (item: PDFItemType) => void
  // onUploadFiles?: (urlList: string[]) => Promise<void>
}

// export interface PdfListComponentHandles {
//   closeImportFileModal: () => void
// }

export const PdfList: React.FC<PDFListProps> = ({ pdfs, selectedPdf, onChangePdf, onDelete }) => {
  // const { t } = useTranslation([Namespace.DEEPEXPLORE, Namespace.GLOBAL])

  // const [openModal, setOpenModal] = useState(false)

  // const handleConfirmPaste = async (url: string) => {
  //   const res = await uploadUrl({
  //     url,
  //     processId,
  //   })
  //   if (res.pdf?.pdfId) {
  //     setOpenModal(false)
  //     onPasteUrl(res.pdf)
  //   } else if (res.errCode === 'notPdfUrl') {
  //     setOpenModal(false)
  //     toast(t('fileCheck.failed'), {
  //       style: {
  //         backgroundColor: 'rgb(255, 255, 255)',
  //         color: '#ee2f2f',
  //         borderRadius: '8px',
  //         padding: '24px',
  //       },
  //     })
  //   } else {
  //     // setOpenModal(false)
  //     toast(t('global:error.systemError'), {
  //       style: {
  //         backgroundColor: 'rgb(255, 255, 255)',
  //         color: '#ee2f2f',
  //         borderRadius: '8px',
  //         padding: '24px',
  //       },
  //     })
  //   }
  // }

  // const handleUploadFiles = async (urlList: string[]) => {
  //   const uploadPromises = urlList.map((url) => {
  //     return uploadFiles({
  //       processId: processId,
  //       s3Key: url,
  //     })
  //   })

  //   try {
  //     const results = await Promise.all(uploadPromises)
  //     console.log(222, results) // 所有的上传结果
  //     setOpenModal(false)
  //     // 刷新左侧 List
  //   } catch (error) {
  //     console.error('上传失败:', error)
  //     // toast
  //   }
  // }

  // useImperativeHandle(ref, () => {
  //   return {
  //     closeImportFileModal: () => {
  //       setOpenModal(false)
  //     },
  //   }
  // }, [])

  return (
    <>
      <div className={'hide-scrollbar flex h-screen-minus-header flex-col overflow-scroll p-3'}>
        <div className='hide-scrollbar center flex h-screen-minus-header flex-col gap-[5px] overflow-y-scroll pb-3'>
          {pdfs?.map((item, idx) => (
            <div
              key={idx}
              onClick={() => {
                onChangePdf(item)
              }}>
              <PdfCard
                item={item}
                selected={selectedPdf?.pdfId === item.pdfId}
                onDelete={onDelete}
              />
            </div>
          ))}
        </div>
        {/* <Button
            className='bottom-0 h-9 w-full rounded-sm'
            onClick={() => {
              setOpenModal(true)
            }}>
            <ArrowUpTrayIcon className='mr-1 h-4 w-4' />
            <Text type={TextEnum.Body_medium}> {t('importFile.button')} </Text>
          </Button> */}
      </div>

      {/* <ImportFilesModal
          open={openModal}
          onClose={() => {
            setOpenModal(false)
          }}
          onConfirmPaste={handleConfirmPaste}
          onUploadFiles={onUploadFiles}
        /> */}
    </>
  )
}

PdfList.displayName = 'PdfList'
