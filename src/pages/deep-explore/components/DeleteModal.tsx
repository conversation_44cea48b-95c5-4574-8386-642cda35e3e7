import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export interface DeleteModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => Promise<void>
}
const DeleteModal: React.FC<DeleteModalProps> = ({
  open,
  onClose,
  onConfirm,
}: DeleteModalProps) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.DEEPEXPLORE])
  const [loading, setLoading] = useState(false)

  const handleCloseModal = () => {
    if (onClose) onClose()
  }
  const handleConfirm = async () => {
    if (onConfirm) {
      setLoading(true)
      await onConfirm()
      setLoading(false)
    }
  }

  return (
    <>
      <Modal open={open} className='w-[420px] break-all p-6'>
        <div>
          <Text type={TextEnum.H4}> {t('deepExplore:deleteModal.title')}</Text>
          <Text type={TextEnum.Body_big} className='mt-4'>
            {t('deepExplore:deleteModal.content')}
          </Text>
        </div>
        <div className='mt-5 flex w-full justify-end gap-2'>
          <Button onClick={handleCloseModal} variant='secondary' disabled={loading}>
            {t('global:button.cancel')}
          </Button>

          <Button disabled={loading} onClick={handleConfirm}>
            {t('global:button.confirm')}
          </Button>
        </div>
      </Modal>
    </>
  )
}

export default DeleteModal
