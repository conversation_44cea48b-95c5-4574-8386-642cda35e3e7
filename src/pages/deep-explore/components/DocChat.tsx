import { ChangeEvent, useEffect, useRef, useState, memo } from 'react'
import { TextEnum, Text } from '@/components/business/text/'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import StopModal from '@/pages/basic-search/chat/StopModal'
import { MessageRoleEnum } from '@/api/getSession'
import MarkdownParser from '@/components/business/markdown-parser'
import EmptyConent from '@/components/business/recommend/img/EmptyContent'
import SmartiesAvatar from '@/components/business/recommend/img/SmartiesAvatar'
import { Textinput } from '@/components/business/text-input'
import { useUserStore } from '@/store/userStore'
import { ShareTypeEnum, SubscriptionEnum } from '@/types'
import { getUser } from '@/api/getUser'
import {
  ParentSessionType,
  PdfFailedEnum,
  PDFItemType,
  PdfPermissionEnum,
  PDFStatusEnum,
} from '@/api/pdfs/getPdf'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { Copy } from '@/components/business/copy'
import LoadingDots from '@/components/business/loading-dots'
import { ChatUnReadyPage } from './ChatUnReadyPage'
import { ChatTag } from './ChatTag'
import { pdfStart, PdfStartRequest, QuickPDFInquiryEnum } from '@/api/pdfs/pdfStart'
import { PdfMessageItem, pdfSession } from '@/api/pdfs/pdfSession'
import { pdfRunning } from '@/api/pdfs/pdfRunning'
import { pdfCancel } from '@/api/pdfs/pdfCancel'
import { PdfProcessMessage, PdfProcessStatusEnum } from '../utils'
import { toast } from 'sonner'
import { Tracking } from '@/lib/tracking'
import { TrackingEventType } from '@/lib/tracking/types'
import { SourceTypeEnum } from '@/api/report/saveSource'
import { SelectDropdown } from '@/components/business/select-dropdown'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'
import { throttle } from 'lodash'
import ShareLink from '@/components/business/share-link'

export interface DocChatProps {
  selectedPdf?: PDFItemType
  onChangePdfProcessId: (id: string) => void
  chatMessageStream: PdfProcessMessage | undefined
  onChangePdfNumber: ({ pageNumber }: { pageNumber: number }) => void
  parentSession?: ParentSessionType
  checkSocket: () => boolean
  source?: 'file' | 'pdf'
  saveToReport?: boolean
}

const DocChat: React.FC<DocChatProps> = memo(
  ({
    selectedPdf,
    onChangePdfProcessId,
    chatMessageStream,
    onChangePdfNumber,
    parentSession,
    checkSocket,
    source,
    saveToReport = true,
  }) => {
    const { t } = useTranslation([Namespace.DEEPEXPLORE, Namespace.GLOBAL])

    const router = useRouter()

    const user = useUserStore((state) => state.user)
    const updateUser = useUserStore((state) => state.updateUser)

    const [messages, setMessages] = useState<PdfMessageItem[]>([])
    const [pageStatus, setPageStatus] = useState<
      'processing' | 'retry' | 'upgrade' | 'overMaxLimits' | 'normal' | undefined
    >()

    const [showStopButton, setShowStopButton] = useState(false) // bottom input status
    const [openStopModal, setOpenStopModal] = useState(false)
    const [openCreditsModal, setOpenCreditsModal] = useState(false)

    const lastUserQuestionRef = useRef<HTMLDivElement>(null)

    // 当前 PDF 会话 session
    const pdfSessionIdRef = useRef<string | undefined>(undefined)
    const pdfProcessIdRef = useRef<string | undefined>(undefined)

    const [inputValue, setInputValue] = useState('')
    const [querying, setQuerying] = useState(false)
    const canceledRef = useRef<boolean>(false)

    // 当前是否有 failed
    const [queryFailed, setQueryFailed] = useState(false)
    const [exceedLimit, setExceedLimit] = useState(false)

    const containerRef = useRef<HTMLDivElement>(null)
    const manualScrolledRef = useRef(false)

    useEffect(() => {
      // reset
      setMessages([])
      setShowStopButton(false)
      setQuerying(false)
      pdfSessionIdRef.current = undefined
      setQueryFailed(false)
      canceledRef.current = false

      // 获取 聊天 记录
      if (selectedPdf?.pdfId) {
        ;(async () => {
          const messages = await queryChatList(selectedPdf.pdfId)
          setMessages(messages ?? [])
          scrollChatList()
          if (messages && messages?.length > 0) {
            const res = await pdfRunning({
              pdfId: selectedPdf.pdfId,
            })
            if (res?.pdfProcess?.pdfProcessId) {
              pdfSessionIdRef.current = res.pdfProcess.pdfSessionId
              pdfProcessIdRef.current = res.pdfProcess.pdfProcessId
              setQuerying(true)
              onChangePdfProcessId(res.pdfProcess.pdfProcessId)
              setShowStopButton(true)
            }
            onChangePdfProcessId('')
          }
          onChangePdfProcessId('')
        })()
      }
    }, [selectedPdf?.pdfId])

    useEffect(() => {
      // 更新 页面 状态
      if (selectedPdf?.pdfPermission === PdfPermissionEnum.TOO_LARGE) {
        setPageStatus('overMaxLimits')
      } else if (selectedPdf?.pdfPermission === PdfPermissionEnum.OVER_PAGE) {
        setPageStatus('upgrade')
      } else {
        if (
          selectedPdf?.status === PDFStatusEnum.DOWNLOAD_SUCCESS ||
          selectedPdf?.status === PDFStatusEnum.UPLOADING ||
          selectedPdf?.status === PDFStatusEnum.OCR_SUCCESS ||
          selectedPdf?.status === PDFStatusEnum.INDEXING ||
          selectedPdf?.status === PDFStatusEnum.OCRING
        ) {
          setPageStatus('processing')
        } else if (
          selectedPdf?.status === PDFStatusEnum.INDEX_FAILED ||
          selectedPdf?.status === PDFStatusEnum.OCR_FAILED
        ) {
          setPageStatus('retry')
        } else if (selectedPdf?.status === PDFStatusEnum.INDEX_SUCCESS) {
          setPageStatus('normal')
        } else {
          setPageStatus(undefined)
        }
      }
    }, [selectedPdf?.pdfId, selectedPdf?.status])

    useEffect(() => {
      if (chatMessageStream?.data?.status === PdfProcessStatusEnum.IN_PROGRESS) {
        setQuerying(true)
      }
      if (
        chatMessageStream?.data?.status === PdfProcessStatusEnum.FINISH ||
        chatMessageStream?.data?.status === PdfProcessStatusEnum.OUTPUTING
      ) {
        setQuerying(false)
      }

      if (chatMessageStream?.data?.status === PdfProcessStatusEnum.FINISH) {
        setShowStopButton(false)
      }

      if (chatMessageStream?.data?.status === PdfProcessStatusEnum.FAILED) {
        if (chatMessageStream?.data.error === PdfFailedEnum.EXCEED_LIMIT) {
          setExceedLimit(true)
        } else {
          setQueryFailed(true)
        }
        setShowStopButton(false)
        setQuerying(false)
      }

      if (
        chatMessageStream?.data.status === PdfProcessStatusEnum.OUTPUTING &&
        canceledRef.current === false
      ) {
        const currentAnswer = messages.find(
          (item) => item.pdfProcessId === chatMessageStream?.pdfProcessId,
        )

        if (currentAnswer) {
          const updatedMessages = messages.map((item) =>
            item.pdfProcessId === chatMessageStream?.pdfProcessId
              ? { ...item, content: chatMessageStream?.data?.message ?? '' }
              : item,
          )
          setMessages(updatedMessages)
        } else {
          setMessages([
            ...messages,
            {
              role: MessageRoleEnum.ASSISTANT,
              content: chatMessageStream?.data?.message ?? '',
              pdfProcessId: chatMessageStream?.pdfProcessId ?? '',
            },
          ])
        }
      }
      scrollChatList()
    }, [chatMessageStream, chatMessageStream?.pdfProcessId, chatMessageStream?.data?.message])

    // get 聊天记录
    const queryChatList = async (pdfId: string): Promise<PdfMessageItem[]> => {
      try {
        const chatList = await pdfSession({ pdfId })
        if (
          chatList === null ||
          !chatList.pdfSession?.messages ||
          chatList.pdfSession?.messages.length <= 0
        ) {
          pdfSessionIdRef.current = undefined
        } else if (chatList.pdfSession.messages) {
          pdfSessionIdRef.current = chatList.pdfSession.pdfSessionId
          return chatList.pdfSession.messages
        }
      } catch (error) {
        return []
      }
      return []
    }

    const updateCredits = async () => {
      const user = await getUser()
      if (user) updateUser(user)
      return user
    }

    // 查询余额
    const checkCredits = async () => {
      // 校验次数
      if (
        user?.currentPlan === SubscriptionEnum.FREE ||
        user?.currentPlan === SubscriptionEnum.BASIC_MONTH ||
        user?.currentPlan === SubscriptionEnum.BASIC_YEAR
      ) {
        if (user.usage.totalQuota - user.usage.totalUsage <= 0) {
          setOpenCreditsModal(true)
          return false
        }
        return true
      } else if (
        // 不校验次数
        user?.currentPlan === SubscriptionEnum.PRO_MONTH ||
        user?.currentPlan === SubscriptionEnum.PRO_YEAR
      ) {
        return true
      } else {
        setOpenCreditsModal(true)
        return false
      }
    }

    const handleSendQuery = async ({
      query,
      quickInquiry,
    }: {
      query?: string
      quickInquiry?: QuickPDFInquiryEnum
    }) => {
      const res = await checkCredits()
      if (!res) return

      const hasSocket = checkSocket()
      if (!hasSocket) {
        // 网络不稳定，刷新页面
        toast(t('global:error.netError'), {
          duration: 3000,
        })
        return
      }

      Tracking.trackEvent('CLICK_START_CHAT_BUTTON', TrackingEventType.CLICK, {
        value: query,
        meta: router.pathname === smartiesRoutes.deepExplore.preview ? 'UPLOAD' : 'SEARCH',
      })

      if ((query && query.trim().length > 0) || quickInquiry) {
        setQueryFailed(false)
        setInputValue('')
        setQuerying(true)
        canceledRef.current = false

        let userMessage = ''
        if (quickInquiry) {
          userMessage =
            quickInquiry === QuickPDFInquiryEnum.DESCRIBE
              ? t('chat.summarizeMessage')
              : (parentSession?.title ?? '')
        } else {
          userMessage = query!
        }
        // update messageList
        setMessages([
          ...messages,
          {
            role: MessageRoleEnum.USER,
            content: userMessage,
          } as PdfMessageItem,
        ])

        manualScrolledRef.current = false
        scrollChatList()

        try {
          const opt =
            quickInquiry === QuickPDFInquiryEnum.DESCRIBE
              ? ({
                  pdfId: selectedPdf?.pdfId,
                  quickInquiry,
                  pdfSessionId: pdfSessionIdRef.current,
                } as PdfStartRequest)
              : ({
                  message:
                    quickInquiry === QuickPDFInquiryEnum.OLD_QUESTION
                      ? parentSession?.title
                      : query,
                  pdfId: selectedPdf?.pdfId,
                  pdfSessionId: pdfSessionIdRef.current,
                } as PdfStartRequest)
          if (source) {
            opt.source = source
          }
          const resp = await pdfStart(opt as PdfStartRequest)
          setShowStopButton(true)

          pdfSessionIdRef.current = resp.pdfSessionId
          pdfProcessIdRef.current = resp.pdfProcessId
          onChangePdfProcessId(resp.pdfProcessId)

          await updateCredits()
        } catch (error) {
          setShowStopButton(false)
          setQuerying(false)
          setQueryFailed(true)
        }
      }
    }

    // 取消
    const handleCancelQuery = async () => {
      // 取消成功
      if (pdfProcessIdRef.current) {
        // 停止输出
        canceledRef.current = true
        const res = await pdfCancel({
          pdfProcessId: pdfProcessIdRef.current,
        })
        if (res?.pdfProcess) {
          setQuerying(false)
          setShowStopButton(false)
          await updateCredits()
          setOpenStopModal(false)
        }
      }
    }

    // click describle btn
    const handleClickSummarizeTag = async (type: QuickPDFInquiryEnum) => {
      handleSendQuery({
        quickInquiry: type,
      })
    }

    const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
      setInputValue(e.target.value)
    }

    // 滚动屏幕
    const scrollChatList = () => {
      setTimeout(() => {
        if (lastUserQuestionRef.current && !manualScrolledRef.current) {
          lastUserQuestionRef.current.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    }

    useEffect(() => {
      const handleUserScroll = throttle(() => {
        manualScrolledRef.current = true
      }, 200)

      const container = containerRef.current
      if (container) {
        container.addEventListener('wheel', handleUserScroll)
        container.addEventListener('touchmove', handleUserScroll)
      }

      return () => {
        if (container) {
          container.removeEventListener('wheel', handleUserScroll)
          container.removeEventListener('touchmove', handleUserScroll)
        }
      }
    }, [containerRef.current])

    return (
      <>
        <div className='flex h-[100%] flex-col justify-between p-1'>
          <div className='absolute right-4 top-[70px] z-50'>
            <ShareLink
              shareType={ShareTypeEnum.PDF}
              shareData={{
                pdfId: selectedPdf?.pdfId,
                pdfName: (selectedPdf?.fileName || selectedPdf?.title)?.replace('.pdf', ''),
              }}
            />
          </div>
          {/* status  */}
          {pageStatus !== 'normal' && (
            <ChatUnReadyPage selectedPdf={selectedPdf} pageStatus={pageStatus} />
          )}
          {/* message */}
          {pageStatus === 'normal' &&
            (messages.length === 0 ? (
              <EmptyConent />
            ) : (
              <div
                className='hide-scrollbar flex-1 overflow-scroll'
                id='ai-smarties-pdf-chat-content'
                ref={containerRef}>
                {messages.map((item, index) => {
                  const ref =
                    item.role === MessageRoleEnum.USER &&
                    (index === messages.length - 1 || index === messages.length - 2)
                      ? lastUserQuestionRef
                      : null

                  return (
                    <div className='flex p-3' key={index}>
                      <div className='shrink-0'>
                        <SmartiesAvatar type={item.role} className='mr-3' />
                      </div>
                      {item.role === MessageRoleEnum.USER ? (
                        <div ref={ref}>
                          <MarkdownParser
                            content={item.content}
                            onClickPageNumber={onChangePdfNumber}
                          />
                        </div>
                      ) : (
                        <div className='w-[calc(100%-32px)] flex-1'>
                          <MarkdownParser
                            content={item.content}
                            onClickPageNumber={onChangePdfNumber}
                          />
                          <Copy
                            question={messages[index - 1].content}
                            sourceType={SourceTypeEnum.PDF}
                            sourceFromId={selectedPdf!.pdfId}
                            content={item.content}
                            sourceId={item.reportSourceId}
                            procId={item.pdfProcessId}
                            source={source}
                            saveToReport={saveToReport}
                          />
                        </div>
                      )}
                    </div>
                  )
                })}

                {querying && (
                  <div className='flex p-3'>
                    <div className='shrink-0'>
                      <SmartiesAvatar type={MessageRoleEnum.ASSISTANT} className='mr-3' />
                    </div>
                    <div className='mt-1 flex items-center gap-2'>
                      <LoadingDots size={4} />
                      <Text type={TextEnum.Body_medium}> {t('statustext.summarize')} </Text>
                    </div>
                  </div>
                )}

                {queryFailed && (
                  <div className='flex p-3'>
                    <div className='shrink-0'>
                      <SmartiesAvatar type={MessageRoleEnum.ASSISTANT} className='mr-3' />
                    </div>
                    <Text type={TextEnum.Body_medium} className='text-dangerous-main mt-2'>
                      {t('global:error.systemError')}
                    </Text>
                  </div>
                )}

                {exceedLimit && (
                  <div className='flex p-3'>
                    <div className='shrink-0'>
                      <SmartiesAvatar type={MessageRoleEnum.ASSISTANT} className='mr-3' />
                    </div>
                    <Text type={TextEnum.Body_medium} className='text-dangerous-main mt-2'>
                      {t('deepExplore:status.exceedLimit')}
                    </Text>
                  </div>
                )}
                <SelectDropdown
                  dropdownWidth={110}
                  type='pdf'
                  sourceId={selectedPdf?.pdfId}
                  showListKey={['saveToDoc', 'close']}></SelectDropdown>
              </div>
            ))}
          {/* footer */}
          {pageStatus === 'normal' && (
            <div className='shrink-0 p-3'>
              <div className='mb-2 flex flex-row gap-1'>
                <ChatTag
                  text={t('chat.summarizePdf')}
                  disabled={showStopButton || querying}
                  onClick={() => {
                    handleClickSummarizeTag(QuickPDFInquiryEnum.DESCRIBE)
                  }}
                />
                {parentSession?.title && (
                  <ChatTag
                    text={parentSession?.title ?? ''}
                    disabled={showStopButton || querying}
                    onClick={() => {
                      handleClickSummarizeTag(QuickPDFInquiryEnum.OLD_QUESTION)
                    }}
                    className='w-fit max-w-[200px]'
                  />
                )}
              </div>
              <Textinput
                withIcon={true}
                placeholder={t('chat.inputPlacehoder')}
                maxLength={300}
                className='flex-1 shrink-0'
                onSearch={(value: string) => {
                  handleSendQuery({
                    query: value,
                  })
                }}
                onEnter={(value: string) => {
                  handleSendQuery({
                    query: value,
                  })
                }}
                showStop={showStopButton}
                disabled={
                  !querying &&
                  inputValue.trim() === '' &&
                  chatMessageStream?.data.status !== 'OUTPUTING'
                }
                onStop={() => {
                  setOpenStopModal(true)
                }}
                value={inputValue}
                onChange={handleChange}
              />
            </div>
          )}
        </div>
        <StopModal
          open={openStopModal}
          onClose={() => {
            setOpenStopModal(false)
          }}
          onConfirm={handleCancelQuery}
        />
        <CreditsModal
          open={openCreditsModal}
          onClose={() => {
            setOpenCreditsModal(false)
          }}
        />
      </>
    )
  },
)
DocChat.displayName = 'DocChat'
export default DocChat
