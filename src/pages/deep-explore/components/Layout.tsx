import { ParentSessionType, PDFItemType, PDFStatusEnum } from '@/api/pdfs/getPdf'
import { Text, TextEnum } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { Namespace } from '@/i18n'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { PdfProcessMessage } from '../utils'
import DocChat from './DocChat'
import { Header } from './Header'
import PdfPreview, { PDFPreviewComponentHandles } from './PdfPreview.client'

interface ContentLayoutProps {
  parentSession?: ParentSessionType
  selectedPdf?: PDFItemType
  onDelete: (item?: PDFItemType) => void
  onChangePdfProcessId: (id: string) => void
  chatMessageStream: PdfProcessMessage | undefined
  checkSocket: () => boolean
}

export const ContentLayout: React.FC<ContentLayoutProps> = ({
  parentSession,
  selectedPdf,
  onDelete,
  onChangePdfProcessId,
  chatMessageStream,
  checkSocket,
}) => {
  const { t } = useTranslation(Namespace.DEEPEXPLORE)

  const childRef = useRef<PDFPreviewComponentHandles | null>(null)

  const [pageStatus, setPageStatus] = useState<string>('loading')

  useEffect(() => {
    if (
      !selectedPdf ||
      selectedPdf?.status === PDFStatusEnum.INIT ||
      selectedPdf?.status === PDFStatusEnum.DOWNLOADING ||
      selectedPdf?.status === PDFStatusEnum.UPLOADING
    ) {
      setPageStatus('loading')
    } else if (selectedPdf?.status === PDFStatusEnum.DOWNLOAD_FAILED) {
      setPageStatus('download_failed')
    } else {
      setPageStatus('')
    }
  }, [selectedPdf?.status])

  const handleClickedPdfNumber = ({ pageNumber }: { pageNumber: number }) => {
    if (childRef.current) {
      childRef.current.updateInputPage(pageNumber)
    }
  }

  return (
    <>
      <div className='flex h-screen-minus-header flex-col'>
        <div className='h-11 w-full overflow-hidden bg-card'>
          {/* {pageStatus === 'loading' ? (
            <div className='flex h-11 items-center'>
              <Skeleton className='ml-4 h-5 w-[400px] rounded bg-skeleton' />
            </div>
          ) : ( */}
          <Header parentSession={parentSession} selectedPdf={selectedPdf} />
          {/* )} */}
        </div>
        <div className='flex h-screen-minus-2-header flex-grow'>
          {pageStatus === 'download_failed' || pageStatus === 'loading' ? (
            <div className='flex-center w-full flex-col'>
              {pageStatus === 'download_failed' ? (
                <>
                  <Image src='/images/download_failed.png' alt='' width={48} height={48} />
                  <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
                    {t('downloadFailed')}
                  </Text>
                  <Button
                    onClick={() => {
                      onDelete(selectedPdf)
                    }}
                    className='mt-5 h-9 w-[196px] rounded-sm'>
                    {t('removePdf')}
                  </Button>
                </>
              ) : (
                <>
                  <Image
                    src='/images/downloading.png'
                    alt=''
                    width={48}
                    height={48}
                    className='animate-spin360'
                  />
                  <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
                    {t('loading')}
                  </Text>
                </>
              )}
            </div>
          ) : (
            <ResizablePanelGroup direction='horizontal' className='overflow-hidden'>
              <ResizablePanel
                defaultSize={71}
                style={{
                  minWidth: '420px',
                }}>
                <div className='h-full min-w-[420px] flex-grow'>
                  <PdfPreview selectedPdf={selectedPdf} ref={childRef} />
                </div>
              </ResizablePanel>
              <ResizableHandle
                withHandle
                className='w-[2px] shrink-0 bg-border hover:bg-primary active:bg-primary'
              />
              <ResizablePanel
                defaultSize={29}
                style={{
                  minWidth: '420px',
                  // width: '420px',
                }}>
                <div className='h-full min-w-[420px]'>
                  <DocChat
                    selectedPdf={selectedPdf}
                    onChangePdfProcessId={onChangePdfProcessId}
                    chatMessageStream={chatMessageStream}
                    onChangePdfNumber={handleClickedPdfNumber}
                    parentSession={parentSession}
                    checkSocket={checkSocket}
                  />
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          )}
        </div>
      </div>
    </>
  )
}
