import Image from 'next/image'
import { Text, TextEnum } from '@/components/business/text'
import clsx from 'clsx'
import { PDFItemType, PDFStatusEnum } from '@/api/pdfs/getPdf'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { useMemo, useState } from 'react'
import { ArrowPathIcon, TrashIcon } from '@heroicons/react/24/outline'
import { pdfRetry } from '@/api/pdfs/pdfRetry'

interface PdfCardProps {
  item: PDFItemType
  selected: boolean
  onDelete?: (item: PDFItemType) => void
}

export const PdfCard: React.FC<PdfCardProps> = ({ item, selected, onDelete }) => {
  const { t } = useTranslation(Namespace.DEEPEXPLORE)
  const [loading, setLoading] = useState<boolean>(false)

  const handleRetry = async (item?: PDFItemType) => {
    if (loading) return

    if (item?.pdfId) {
      setLoading(true)
      try {
        await pdfRetry({
          pdfId: item.pdfId,
        })
        setLoading(false)
      } catch (error) {
        setLoading(false)
      }
    }
  }

  return (
    <div
      className={clsx(
        'group w-[230px] cursor-pointer rounded-sm border-[1px] hover:border-primary hover:bg-primary-hover',
        selected ? 'border-primary bg-primary-hover' : 'border-border bg-card',
      )}>
      <div className='flex-v-center p-2'>
        <Image src={'/images/pdf_icon.svg'} alt='pdf icon' width={24} height={24} />
        <Text type={TextEnum.H6} className='overflow-hidden text-ellipsis whitespace-nowrap'>
          {item.status === PDFStatusEnum.DOWNLOAD_FAILED
            ? t('pdfCard.failedTitle')
            : item.status === PDFStatusEnum.UPLOADING
              ? t('pdfCard.uploadingTitle')
              : item.title}
        </Text>
      </div>
      {item.snippet && (
        <Text
          type={TextEnum.Body_small}
          className='mb-2 mt-1 h-8 overflow-hidden text-ellipsis break-words px-2 text-secondary-black-2'>
          {item.snippet}
        </Text>
      )}
      <div className='flex h-8 justify-between border-t-[1px] p-2'>
        <Tag status={item.status} />
        <div
          className={clsx(
            'group-hover:flex-center rounded-r-sm pl-2 group-hover:bg-primary-hover',
          )}>
          <TrashIcon
            className='group-hover:flex-center mr-2 hidden h-4 w-4 shrink-0'
            onClick={() => {
              onDelete?.(item)
            }}
          />
          {item.status === PDFStatusEnum.DOWNLOAD_FAILED && (
            <ArrowPathIcon
              className='mr-2 h-4 w-4 shrink-0'
              onClick={() => {
                handleRetry(item)
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}

const Tag = ({ status }: { status: PDFStatusEnum }) => {
  const { t } = useTranslation(Namespace.DEEPEXPLORE)

  const tagNames: Record<PDFStatusEnum, string> = useMemo(() => {
    return {
      [PDFStatusEnum.INIT]: t('pdfCard.tags.downloading'),
      [PDFStatusEnum.DOWNLOADING]: t('pdfCard.tags.downloading'),
      [PDFStatusEnum.DOWNLOAD_SUCCESS]: '', //
      [PDFStatusEnum.DOWNLOAD_FAILED]: t('pdfCard.tags.downloadFailure'),
      [PDFStatusEnum.UPLOADING]: t('pdfCard.tags.uploading'),
      [PDFStatusEnum.OCRING]: '',
      [PDFStatusEnum.OCR_SUCCESS]: '',
      [PDFStatusEnum.OCR_FAILED]: t('pdfCard.tags.failure'),
      [PDFStatusEnum.INDEXING]: t('pdfCard.tags.processing'),
      [PDFStatusEnum.INDEX_SUCCESS]: t('pdfCard.tags.success'),
      [PDFStatusEnum.INDEX_FAILED]: t('pdfCard.tags.failure'),
    }
  }, [t])

  let classes = ''
  switch (status) {
    case PDFStatusEnum.INIT:
    case PDFStatusEnum.DOWNLOADING:
    case PDFStatusEnum.UPLOADING:
    case PDFStatusEnum.INDEXING:
      classes = 'bg-info-10 text-info'
      break
    case PDFStatusEnum.DOWNLOAD_FAILED:
    case PDFStatusEnum.OCR_FAILED:
    case PDFStatusEnum.INDEX_FAILED:
      classes = 'bg-dangerous-10 text-dangerous'
      break
    case PDFStatusEnum.INDEX_SUCCESS:
      classes = 'bg-success-10 text-success'
      break
    default:
      classes = ''
      break
  }
  return (
    <div className={clsx('flex-v-center w-fit rounded-[2px] px-1', classes)}>
      <Text type={TextEnum.Body_small}>{tagNames[status]}</Text>
    </div>
  )
}
