/* eslint-disable @next/next/no-img-element */
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { TextEnum, Text } from '@/components/business/text'
import { CheckBadgeIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { Button } from '@/components/ui/button'

export interface ContentItem {
  value: string
  support: boolean
}
interface PricingCardProps {
  title: string
  subTitle?: string
  pricingNode: React.ReactNode
  contents: {
    credits?: ContentItem[]
    exploration?: ContentItem[]
    file?: ContentItem[]
    exclusiveFeat?: ContentItem[]
    bespoke?: ContentItem[]
  }
  buttonType?:
    | 'link'
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | null
    | undefined
  buttonText: string
  onClick?: () => void
  disabled: boolean
  loading: boolean
}

export const ContentList = ({
  title,
  list,
}: {
  title: string
  list: Array<{
    value: string
    support: boolean
  }>
}) => {
  return (
    <>
      <Text type={TextEnum.H4} className='mb-2.5 mt-7'>
        {title}
      </Text>
      {(list ?? []).map((item, idx) => {
        return (
          <div key={idx} className='mb-2 flex gap-2'>
            <CheckBadgeIcon
              className={clsx(
                'mt-1 h-4 w-4 shrink-0',
                item.support ? 'text-primary' : 'text-secondary-black-3',
              )}
            />
            <Text
              type={TextEnum.Body_big}
              className={clsx(item.support ? 'text-secondary-black-1' : 'text-secondary-black-3')}>
              {item.value}
            </Text>
          </div>
        )
      })}
    </>
  )
}
const PricingCard = ({
  title,
  subTitle,
  pricingNode,
  contents,
  buttonText,
  onClick,
  disabled,
  buttonType = 'default',
  loading,
}: PricingCardProps) => {
  const { t } = useTranslation(Namespace.PRICING)

  return (
    <>
      <div className='flex w-[296px] flex-col rounded-sm bg-card p-6'>
        <div className='flex items-center justify-between'>
          <Text type={TextEnum.H4}>{title}</Text>
          {subTitle && (
            <Text
              type={TextEnum.Body_medium}
              className='rounded bg-primary-bg px-1 py-0.5 text-primary'>
              {subTitle}
            </Text>
          )}
        </div>

        <div className='flex-v-center mt-4'>{pricingNode}</div>

        <Button
          className={'mb-5 mt-8 w-full'}
          onClick={onClick}
          disabled={disabled || loading}
          variant={buttonType}>
          {loading && <img src='/images/loading.svg' className='mr-1 h-4 w-4 animate-spin-slow' />}
          {buttonText}
        </Button>

        {contents.credits ? (
          <ContentList title={t('card.content.credits.title')} list={contents.credits ?? []} />
        ) : null}
        {contents.exploration ? (
          <ContentList
            title={t('card.content.exploration.title')}
            list={contents.exploration ?? []}
          />
        ) : null}

        {contents.file ? (
          <ContentList title={t('card.content.fileStorage.title')} list={contents.file ?? []} />
        ) : null}

        {contents.exclusiveFeat ? (
          <ContentList
            title={t('card.content.exclusiveFeature.title')}
            list={contents.exclusiveFeat ?? []}
          />
        ) : null}
        {contents.bespoke ? (
          <ContentList title={t('card.content.bespoke.title')} list={contents.bespoke ?? []} />
        ) : null}
      </div>
    </>
  )
}

export default PricingCard
