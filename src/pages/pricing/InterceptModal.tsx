import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Namespace } from '@/i18n'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export interface InterceptModalProps {
  open: boolean
  onClose: () => void
  onConfirm: (code: string) => void
}

const InterceptModal: React.FC<InterceptModalProps> = ({
  open,
  onClose,
  onConfirm,
}: InterceptModalProps) => {
  const { t } = useTranslation([Namespace.GLOBAL])

  const [code, setCode] = useState<string>('')

  const handleCancel = () => {
    if (onClose) onClose()
  }
  const handleConfirm = () => {
    if (onConfirm) onConfirm(code)
  }

  return (
    <>
      <Modal
        showCloseIcon
        open={open}
        className='w-[434px] break-all p-6'
        onClose={handleCancel}
        overlayClose={false}>
        <>
          <Text type={TextEnum.H4} className='mb-5'>
            {t('prompt.title')}
          </Text>

          <Text type={TextEnum.Body_big} className='mb-5'>
            {t('prompt.content')}
          </Text>

          <Input
            className='mb-6'
            onChange={(e) => {
              setCode(e.target.value)
            }}
          />

          <div className='text-right'>
            <Button onClick={handleCancel} variant='outline'>
              {t('button.cancel')}
            </Button>
            <Button onClick={handleConfirm} className='ml-2' disabled={code.trim().length === 0}>
              {t('button.confirm')}
            </Button>
          </div>
        </>
      </Modal>
    </>
  )
}

export default InterceptModal
