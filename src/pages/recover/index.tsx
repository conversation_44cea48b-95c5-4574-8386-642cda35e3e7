import { useEffect } from 'react'
import { Environments, initializePaddle, Paddle, Theme } from '@paddle/paddle-js'
import { useTheme } from '@/context/ThemeContext'
import { getCookie } from '@/lib/cookie'

const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENV! as Environments
const paddleToken = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN!

const Recover = () => {
  const { theme } = useTheme()

  useEffect(() => {
    if (!paddleEnv || !paddleToken) {
      throw Error('system error')
    }
    initializePaddle({
      environment: paddleEnv,
      token: paddleToken,
      pwCustomer: {}, // support retain
    }).then((paddleInstance: Paddle | undefined) => {
      if (paddleInstance) {
        paddleInstance.Update({
          checkout: {
            settings: {
              theme: theme as Theme,
              locale: getCookie('lang') === 'zh' ? 'zh-Hans' : 'en',
            },
          },
        })
      }
    })
  }, [])

  return <></>
}

export default Recover
