import { startQuest } from '@/api/startQuest'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import SearchModeSelect from '@/components/business/searh-mode-select'
import { Text, TextEnum } from '@/components/business/text'
import { Textinput } from '@/components/business/text-input'
import { TextTips } from '@/components/business/text-tips'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import { useUserStore } from '@/store/userStore'
import { PoTypeEnum, SearchModeEnum } from '@/types'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/router'
import * as React from 'react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { checkCredits } from '../management'

const PotentialRisk = () => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const router = useRouter()
  const user = useUserStore((state) => state.user)

  const [query, setQuery] = React.useState<string>('')

  const [disableSend, setDisableSend] = useState(false)
  const [sended, setSended] = useState(false)
  const [openCreditsModal, setOpenCreditsModal] = useState(false)
  const [searchMode, setSearchMode] = useState<SearchModeEnum>(SearchModeEnum.AUTO)

  useEffect(() => {
    if (query.trim()) {
      setDisableSend(false)
    } else {
      setDisableSend(true)
    }
  }, [query])

  const handleClickSend = async () => {
    if (!disableSend && !sended) {
      const checkRes = checkCredits(user)
      if (!checkRes) {
        setOpenCreditsModal(true)
        return false
      }

      setSended(true)
      try {
        const resp = await startQuest({
          message: [
            {
              key: 'description',
              value: query,
            },
          ],
          poType: PoTypeEnum.RISK,
          useReasoning: searchMode === SearchModeEnum.REASONING ? true : undefined,
        })
        if (resp.sessionId && resp.processId) {
          router.push({
            pathname: smartiesRoutes.basicSearch.chat(PoTypeEnum.RISK.toLocaleLowerCase()),
            query: {
              processId: resp.processId,
              sessionId: resp.sessionId,
            },
          })
        } else {
          setSended(false)
        }
      } catch (error) {
        // TODO toast 请求异常，请稍后
        console.error('query', error)
        setSended(false)
      }
    }
  }

  const handleQueryChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuery(e.target.value)
  }
  return (
    <div className='flex w-full min-w-[500px] max-w-[760px] flex-col gap-2.5'>
      <Text type={TextEnum.H3} className='mb-0.5'>
        {t('homePage.card.title_risk')}
      </Text>
      <TextTips title={t('risk.query1')} tips={t('risk.tips1')} />
      <Textinput
        onChange={handleQueryChange}
        placeholder={t('risk.query1Placeholder')}
        maxLength={5000}
        withIcon={false}
        value={query}
        onEnter={handleClickSend}
      />
      <div className='mt-5 flex items-center justify-between'>
        <SearchModeSelect
          searchMode={searchMode}
          onChangeSearchMode={(mode) => {
            setSearchMode(mode)
          }}
        />
        <div className='flex items-center'>
          <Text type={TextEnum.Body_small} className={'text-secondary-black-3'}>
            {t(`homePage.tip`)}
          </Text>
          <Button className='ml-2 px-14' onClick={handleClickSend} disabled={disableSend || sended}>
            <>
              <PaperAirplaneIcon className='mr-1 h-4 w-4' />
              {t('common.send')}
            </>
          </Button>
        </div>
      </div>
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
    </div>
  )
}

export default PotentialRisk
