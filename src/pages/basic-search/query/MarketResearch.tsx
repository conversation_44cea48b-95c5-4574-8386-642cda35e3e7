import * as React from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'
import { But<PERSON> } from '@/components/ui/button'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'
import { startQuest } from '@/api/startQuest'
import { useEffect, useState } from 'react'
import { PoTypeEnum, SearchModeEnum } from '@/types'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { Textinput } from '@/components/business/text-input'
import { TextTips } from '@/components/business/text-tips'
import { checkCredits } from '../management'
import { useUserStore } from '@/store/userStore'
import SearchModeSelect from '@/components/business/searh-mode-select'

const MarketResearch = () => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const router = useRouter()
  const user = useUserStore((state) => state.user)

  const [query1, setQuery1] = useState('')
  const [query2, setQuery2] = useState('')
  const [disableSend, setDisableSend] = useState(false)
  const [sended, setSended] = useState(false)
  const [openCreditsModal, setOpenCreditsModal] = useState(false)
  const [searchMode, setSearchMode] = useState<SearchModeEnum>(SearchModeEnum.AUTO)

  useEffect(() => {
    if (query1.trim() && query2.trim()) {
      setDisableSend(false)
    } else {
      setDisableSend(true)
    }
  }, [query1, query2])

  const handleClickSend = async () => {
    if (!disableSend && !sended) {
      const checkRes = checkCredits(user)
      if (!checkRes) {
        setOpenCreditsModal(true)
        return false
      }

      setSended(true)
      try {
        const resp = await startQuest({
          message: [
            {
              key: 'description',
              value: query1,
            },
            {
              key: 'targetMarket',
              value: query2,
            },
          ],
          poType: PoTypeEnum.MARKET,
          useReasoning: searchMode === SearchModeEnum.REASONING ? true : undefined,
        })
        if (resp.sessionId && resp.processId) {
          router.push({
            pathname: smartiesRoutes.basicSearch.chat(PoTypeEnum.MARKET.toLocaleLowerCase()),
            query: {
              processId: resp.processId,
              sessionId: resp.sessionId,
            },
          })
        } else {
          setSended(false)
        }
      } catch (error) {
        // TODO toast 请求异常，请稍后
        console.error('query', error)
        setSended(false)
      }
    }
  }

  return (
    <div className='flex w-full min-w-[500px] max-w-[760px] flex-col gap-2.5'>
      <Text type={TextEnum.H3} className='mb-[2px]'>
        {t('homePage.card.title_market')}
      </Text>
      <TextTips title={t('market.query1')} tips={t('market.tips1')} />
      <Textinput
        onChange={(e) => {
          setQuery1(e.target.value)
        }}
        placeholder={t('market.query1Placeholder')}
        maxLength={5000}
        value={query1}
        withIcon={false}
        onEnter={handleClickSend}
      />
      <TextTips title={t('market.query2')} tips={t('market.tips2')} />
      <Textinput
        onChange={(e) => {
          setQuery2(e.target.value)
        }}
        placeholder={t('market.query2Placeholder')}
        maxLength={300}
        value={query2}
        withIcon={false}
        onEnter={handleClickSend}
      />
      <div className='mt-5 flex items-center justify-between'>
        <SearchModeSelect
          searchMode={searchMode}
          onChangeSearchMode={(mode) => {
            setSearchMode(mode)
          }}
        />
        <div className='flex items-center'>
          <Text type={TextEnum.Body_small} className={'text-secondary-black-3'}>
            {t(`homePage.tip`)}
          </Text>
          <Button className='ml-2 px-14' onClick={handleClickSend} disabled={disableSend || sended}>
            <>
              <PaperAirplaneIcon className='mr-1 h-4 w-4' />
              {t('common.send')}
            </>
          </Button>
        </div>
      </div>
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
    </div>
  )
}

export default MarketResearch
