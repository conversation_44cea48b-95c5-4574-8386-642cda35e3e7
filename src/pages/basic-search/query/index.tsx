import MarketResearch from './MarketResearch'
import TopicResearch from './TopicResearch'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import SwotAnalysis from './SwotAnalysis'
import CompanyResearch from './CompanyResearch'
import OverseaRegulation from './OverseaRegulation'
import PotentialRisk from './PotentialRisk'
import BasicSearchLayout from '../BasicSearchLayout'

const componentMap: Record<string, React.ReactNode> = {
  market: <MarketResearch />,
  topic: <TopicResearch />,
  swot: <SwotAnalysis />,
  company: <CompanyResearch />,
  regulation: <OverseaRegulation />,
  risk: <PotentialRisk />,
}

const SearchQueryPage = () => {
  const router = useRouter()
  const { type } = router.query

  const [component, setComponent] = useState<React.ReactNode>(null)

  useEffect(() => {
    setComponent(componentMap[type as string] || componentMap.default)
  }, [type])

  return (
    <>
      <BasicSearchLayout className='flex-center h-screen-minus-header min-h-fit w-full pb-6'>
        <div className='flex w-full flex-col items-center px-[130px]'>{component}</div>
      </BasicSearchLayout>
    </>
  )
}

export default SearchQueryPage
