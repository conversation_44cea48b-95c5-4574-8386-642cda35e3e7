import { Modal } from '@/components/business/modal'
import { Button } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { Checkbox } from '@/components/ui/checkbox'
import { useState } from 'react'
export const ModalDeduct = ({
  open,
  handleCancel,
  onConfirm,
}: {
  open: boolean
  handleCancel: () => void
  onConfirm: (check: boolean) => void
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)
  const [check, setCheck] = useState(false)
  const onCheckedChange = (checked: boolean) => {
    setCheck(checked)
  }
  return (
    <Modal open={open} className='w-[434px] break-all p-6'>
      <div>
        <Text type={TextEnum.H4} className='flex items-center justify-between'>
          {t('modal.deductTitle')}
          <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div className='mt-4'>
          <Text type={TextEnum.Body_medium}>{t('modal.promptContent')}</Text>
          <div className='mt-3 flex items-center'>
            <div className='flex-v-center'>
              <Checkbox checked={check} onCheckedChange={onCheckedChange} />
              <Text type={TextEnum.Body_medium} className='ml-2'>
                {t('modal.showAgain')}
              </Text>
            </div>
          </div>
          <div className='mt-2 text-right'>
            <Button variant='secondary' size='sm' className='px-4' onClick={handleCancel}>
              {t('modal.cancel')}
            </Button>
            <Button
              size='sm'
              className='ml-2 px-4'
              onClick={() => {
                onConfirm(check)
              }}>
              {t('modal.confirm')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
