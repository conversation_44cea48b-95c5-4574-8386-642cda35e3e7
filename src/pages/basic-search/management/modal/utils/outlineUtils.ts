import { OutlineItem } from '@/store/deep-research'

// 递归收集所有项目的sequence用于默认展开
export const collectAllSequences = (items: OutlineItem[]): string[] => {
  const sequences: string[] = []
  const traverse = (item: OutlineItem) => {
    if (item.chapterIndex) {
      sequences.push(item.chapterIndex)
    }
    if (item.children) {
      item.children.forEach(traverse)
    }
  }
  items.forEach(traverse)
  return sequences
}

// 验证拖拽目标是否有效
export const isValidDropTarget = (
  sourcePath: number[] | null,
  targetPath: number[],
  position: 'before' | 'after' | 'inside',
): boolean => {
  if (!sourcePath) return false

  // 不能拖拽到自身
  if (JSON.stringify(sourcePath) === JSON.stringify(targetPath)) {
    return false
  }

  // 不能拖拽到自身的子项中
  if (targetPath.length > sourcePath.length) {
    const isChild = sourcePath.every((val, index) => val === targetPath[index])
    if (isChild) return false
  }

  // 根据位置进行额外验证
  if (position === 'inside') {
    // 当位置为 'inside' 时，不能拖拽到自身内部
    if (JSON.stringify(sourcePath) === JSON.stringify(targetPath)) {
      return false
    }
  } else {
    // 当位置为 'before' 或 'after' 时，验证同级操作
    // 不能拖拽到紧邻的位置（这样的操作没有意义）
    if (sourcePath.length === targetPath.length) {
      const sourceIndex = sourcePath[sourcePath.length - 1]
      const targetIndex = targetPath[targetPath.length - 1]
      const parentPathsEqual = sourcePath
        .slice(0, -1)
        .every((val, index) => val === targetPath[index])

      if (parentPathsEqual) {
        // 同一父级下，不能拖拽到自身前面或后面的紧邻位置
        if (position === 'before' && targetIndex === sourceIndex) {
          return false
        }
        if (position === 'after' && targetIndex === sourceIndex) {
          return false
        }
      }
    }
  }

  return true
}

// 根据路径获取项目
export const getItemByPath = (items: OutlineItem[], path: number[]): OutlineItem | null => {
  let current = items
  let item: OutlineItem | null = null

  for (let i = 0; i < path.length; i++) {
    if (!current[path[i]]) return null
    item = current[path[i]]
    if (i < path.length - 1) {
      current = item.children || []
    }
  }

  return item
}

// 根据路径移除项目
export const removeItemByPath = (items: OutlineItem[], path: number[]): OutlineItem | null => {
  if (path.length === 1) {
    return items.splice(path[0], 1)[0]
  }

  const parentPath = path.slice(0, -1)
  const parent = getItemByPath(items, parentPath)
  if (parent && parent.children) {
    return parent.children.splice(path[path.length - 1], 1)[0]
  }

  return null
}

// 在指定路径插入项目
export const insertItemAtPath = (
  items: OutlineItem[],
  item: OutlineItem,
  targetPath: number[],
  position: 'before' | 'after' | 'inside',
) => {
  if (position === 'inside') {
    // 插入为子项
    const target = getItemByPath(items, targetPath)
    if (target) {
      if (!target.children) target.children = []
      target.children.push(item)
    }
  } else {
    // 插入为同级项目
    if (targetPath.length === 1) {
      // 顶级项目
      const insertIndex = position === 'before' ? targetPath[0] : targetPath[0] + 1
      items.splice(insertIndex, 0, item)
    } else {
      // 子级项目
      const parentPath = targetPath.slice(0, -1)
      const parent = getItemByPath(items, parentPath)
      if (parent && parent.children) {
        const insertIndex =
          position === 'before'
            ? targetPath[targetPath.length - 1]
            : targetPath[targetPath.length - 1] + 1
        parent.children.splice(insertIndex, 0, item)
      }
    }
  }
}

// 递归处理大纲数据，重新计算序号
export const processOutlineDataRecursive = (
  items: OutlineItem[],
  level = 0,
  parentSequence = '',
): OutlineItem[] => {
  return items.map((item, index) => {
    const currentSequence = parentSequence ? `${parentSequence}.${index + 1}` : `${index + 1}`
    return {
      ...item,
      level,
      chapterIndex: currentSequence,
      children: item.children
        ? processOutlineDataRecursive(item.children, level + 1, currentSequence)
        : undefined,
    }
  })
}

// 更新大纲项目内容
export const updateOutlineItemContent = (
  items: OutlineItem[],
  itemId: string,
  field: 'title' | 'description',
  value: string,
): OutlineItem[] => {
  const updateItemRecursive = (items: OutlineItem[]): OutlineItem[] => {
    return items.map((item) => {
      if (item.id === itemId) {
        return {
          ...item,
          [field]: value,
        }
      }
      if (item.children) {
        return {
          ...item,
          children: updateItemRecursive(item.children),
        }
      }
      return item
    })
  }

  return updateItemRecursive(items)
}
