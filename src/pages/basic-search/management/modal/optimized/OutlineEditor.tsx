import React, { memo } from 'react'
import { OutlineEditorProps } from './types'
import OutlineItem from './OutlineItem'

/**
 * 大纲编辑器组件
 * 负责渲染左侧的大纲编辑区域
 */
const OutlineEditor: React.FC<OutlineEditorProps> = memo(
  ({
    outline,
    showDescriptions,
    expandedItems,
    onToggleExpand,
    onUpdateItem,
    onDeleteChapter,
    onAddChapter,
    onAddSubChapter,
  }) => {
    return (
      <div
        className='h-full w-[calc(100%-300px)] overflow-auto border-r border-border p-4'
        style={{ scrollbarGutter: 'stable' }}>
        {outline?.map((item, index) => (
          <OutlineItem
            key={item.id}
            item={item}
            isShowDescription={showDescriptions}
            isLast={index === outline.length - 1}
            level={0}
            path={[index]}
            expandedItems={expandedItems}
            onToggleExpand={onToggleExpand}
            onUpdateItem={onUpdateItem}
            onDeleteChapter={onDeleteChapter}
            onAddChapter={onAddChapter}
            onAddSubChapter={onAddSubChapter}
          />
        ))}
      </div>
    )
  },
)

OutlineEditor.displayName = 'OutlineEditor'

export default OutlineEditor
