/**
 * 优化后的大纲模态框组件导出
 *
 * 这个文件提供了所有优化后组件的统一导出入口
 * 使用方式：
 *
 * ```tsx
 * // 导入优化后的主组件
 * import { ModalOutlineOptimized } from './optimized'
 *
 * // 或者导入具体的子组件
 * import { OutlineItem, DraggableOutlineItem } from './optimized'
 * ```
 */

// 主组件
export { ModalOutlineOptimized } from './ModalOutlineOptimized'

// 子组件
export { default as OutlineItem } from './OutlineItem'
export { default as DraggableOutlineItem } from './DraggableOutlineItem'
export { default as OutlineEditor } from './OutlineEditor'
export { default as OutlinePreview } from './OutlinePreview'

// Hooks
export { useDragDrop } from './useDragDrop'
export { useOutlineManagement } from './useOutlineManagement'

// 工具函数
export * from './outlineUtils'
export * from './DragDropStyles'

// 类型定义
export * from './types'

// 默认导出优化后的主组件
export { ModalOutlineOptimized as default } from './ModalOutlineOptimized'
