import React, { memo } from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { OutlinePreviewProps } from './types'
import DraggableOutlineItem from './DraggableOutlineItem'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

/**
 * 大纲预览组件
 * 负责渲染右侧的可拖拽大纲预览区域
 */
const OutlinePreview: React.FC<OutlinePreviewProps> = memo(
  ({
    outline,
    expandedItems,
    dragState,
    onToggleExpand,
    onDragStart,
    onDragOver,
    onDragLeave,
    onDrop,
    onDragEnd,
  }) => {
    const { t } = useTranslation(Namespace.DEEPRESEARCH)

    return (
      <div
        className='flex h-full w-[330px] flex-col gap-2 overflow-auto p-4 pr-2 text-secondary-black-3'
        style={{ scrollbarGutter: 'stable' }}>
        <Text type={TextEnum.H5} className='!font-normal text-secondary-black-1'>
          {t('outline.tableOfContentsPreview')}
        </Text>
        <div className='draggable-outline-container'>
          {outline?.map((item, index) => (
            <DraggableOutlineItem
              key={item.id}
              item={item}
              path={[index]}
              level={0}
              expandedItems={expandedItems}
              dragState={dragState}
              onToggleExpand={onToggleExpand}
              onDragStart={onDragStart}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              onDrop={onDrop}
              onDragEnd={onDragEnd}
            />
          ))}
        </div>
      </div>
    )
  },
)

OutlinePreview.displayName = 'OutlinePreview'

export default OutlinePreview
