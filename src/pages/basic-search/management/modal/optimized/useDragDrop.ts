import { useState, useCallback } from 'react'
import { OutlineItem } from '@/store/deep-research'
import { DragState, DropPosition, UseDragDropReturn } from './types'
import {
  isValidDropTarget,
  reorderNestedOutlineItems,
  parseDragTransferData,
  createDragTransferData,
} from './outlineUtils'
import { cleanupDragClasses } from './DragDropStyles'

/**
 * 拖拽功能的自定义Hook
 * 封装所有拖拽相关的状态和逻辑
 */
export const useDragDrop = (
  outline: OutlineItem[] | undefined,
  onOutlineUpdate: (newOutline: OutlineItem[]) => void,
): UseDragDropReturn => {
  const [dragState, setDragState] = useState<DragState>({
    draggedItem: null,
    draggedPath: null,
    dropTargetPath: null,
    dropPosition: null,
    isDragging: false,
  })

  /**
   * 清理拖拽状态
   */
  const cleanupDragState = useCallback(() => {
    setDragState({
      draggedItem: null,
      draggedPath: null,
      dropTargetPath: null,
      dropPosition: null,
      isDragging: false,
    })
    cleanupDragClasses()
  }, [])

  /**
   * 处理拖拽开始
   */
  const handleDragStart = useCallback((e: React.DragEvent, item: OutlineItem, path: number[]) => {
    const transferData = createDragTransferData(item, path)
    e.dataTransfer.setData('text/plain', transferData)
    e.dataTransfer.effectAllowed = 'move'

    setDragState({
      draggedItem: item,
      draggedPath: path,
      dropTargetPath: null,
      dropPosition: null,
      isDragging: true,
    })

    // 添加拖拽样式
    ;(e.currentTarget as HTMLElement).classList.add('dragging')
  }, [])

  /**
   * 处理拖拽悬停
   */
  const handleDragOver = useCallback(
    (e: React.DragEvent, targetPath: number[], position: DropPosition) => {
      e.preventDefault()
      e.dataTransfer.dropEffect = 'move'

      // 防止拖拽到自身位置或自身的子项
      if (!isValidDropTarget(dragState.draggedPath, targetPath, position)) {
        // 清理无效拖拽目标的状态
        setDragState((prev) => ({
          ...prev,
          dropTargetPath: null,
          dropPosition: null,
        }))
        return
      }

      setDragState((prev) => ({
        ...prev,
        dropTargetPath: targetPath,
        dropPosition: position,
      }))

      // 添加视觉反馈
      const target = e.currentTarget as HTMLElement
      target.classList.add('drag-over')
    },
    [dragState.draggedPath],
  )

  /**
   * 处理拖拽离开
   */
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    const target = e.currentTarget as HTMLElement
    target.classList.remove('drag-over')

    // 清理拖拽状态，避免红色边框一直显示
    setDragState((prev) => ({
      ...prev,
      dropTargetPath: null,
      dropPosition: null,
    }))
  }, [])

  /**
   * 处理拖拽放置
   */
  const handleDrop = useCallback(
    (e: React.DragEvent, targetPath: number[], position: DropPosition) => {
      e.preventDefault()

      const dragData = parseDragTransferData(e.dataTransfer)
      if (!dragData || !outline) {
        cleanupDragState()
        return
      }

      const sourcePath = dragData.sourcePath

      if (isValidDropTarget(sourcePath, targetPath, position)) {
        const newOutline = reorderNestedOutlineItems(outline, sourcePath, targetPath, position)
        onOutlineUpdate(newOutline)
      }

      cleanupDragState()
    },
    [outline, onOutlineUpdate, cleanupDragState],
  )

  /**
   * 处理拖拽结束
   */
  const handleDragEnd = useCallback(
    (e: React.DragEvent) => {
      cleanupDragState()
      ;(e.currentTarget as HTMLElement).classList.remove('dragging')
    },
    [cleanupDragState],
  )

  return {
    dragState,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
    cleanupDragState,
  }
}
