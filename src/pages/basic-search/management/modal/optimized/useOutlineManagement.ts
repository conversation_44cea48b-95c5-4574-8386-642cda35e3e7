import { useState, useCallback, useEffect } from 'react'
import { OutlineItem } from '@/store/deep-research'
import { UseOutlineManagementReturn } from './types'
import {
  collectAllSequences,
  deleteChapterByPath,
  addChapterAfterPath,
  addSubChapterToPath,
} from './outlineUtils'

/**
 * 大纲管理功能的自定义Hook
 * 封装展开/收起、显示/隐藏描述、更新项目等逻辑
 */
export const useOutlineManagement = (
  outline: OutlineItem[] | undefined,
  onOutlineUpdate: (newOutline: OutlineItem[]) => void,
  isOpen: boolean,
): UseOutlineManagementReturn => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [showDescriptions, setShowDescriptions] = useState(true)

  /**
   * 初始化展开状态
   */
  useEffect(() => {
    if (isOpen && outline) {
      // 默认展开所有项目
      const allSequences = collectAllSequences(outline)
      setExpandedItems(new Set(allSequences))
    }
  }, [isOpen, outline])

  /**
   * 切换单个项目的展开状态
   */
  const toggleExpand = useCallback((chapterIndex: string) => {
    setExpandedItems((prev) => {
      const newExpandedItems = new Set(prev)
      if (newExpandedItems.has(chapterIndex)) {
        newExpandedItems.delete(chapterIndex) // 收起
      } else {
        newExpandedItems.add(chapterIndex) // 展开
      }
      return newExpandedItems
    })
  }, [])

  /**
   * 切换全部展开/收起
   */
  const toggleExpandAll = useCallback(() => {
    if (!outline) return

    if (expandedItems.size === 0) {
      // 当前是收起状态，执行展开全部
      const allSequences = collectAllSequences(outline)
      setExpandedItems(new Set(allSequences))
    } else {
      // 当前是展开状态，执行收起全部
      setExpandedItems(new Set())
    }
  }, [outline, expandedItems.size])

  /**
   * 切换显示/隐藏描述
   */
  const toggleShowDescriptions = useCallback(() => {
    setShowDescriptions((prev) => !prev)
  }, [])

  /**
   * 更新大纲项目内容
   */
  const updateOutlineItem = useCallback(
    (itemId: string, field: 'title' | 'description', value: string) => {
      if (!outline) return

      const updateItemRecursive = (items: OutlineItem[]): OutlineItem[] => {
        return items.map((item) => {
          if (item.id === itemId) {
            return {
              ...item,
              [field]: value,
            }
          }
          if (item.children) {
            return {
              ...item,
              children: updateItemRecursive(item.children),
            }
          }
          return item
        })
      }

      const updatedItems = updateItemRecursive(outline)
      onOutlineUpdate(updatedItems)
    },
    [outline, onOutlineUpdate],
  )

  /**
   * 删除章节
   */
  const deleteChapter = useCallback(
    (path: number[]) => {
      if (!outline) return
      const updatedItems = deleteChapterByPath(outline, path)
      onOutlineUpdate(updatedItems)
    },
    [outline, onOutlineUpdate],
  )

  /**
   * 添加章节（在指定位置后）
   */
  const addChapter = useCallback(
    (path: number[]) => {
      if (!outline) return
      const updatedItems = addChapterAfterPath(outline, path)
      onOutlineUpdate(updatedItems)
    },
    [outline, onOutlineUpdate],
  )

  /**
   * 添加子章节
   */
  const addSubChapter = useCallback(
    (path: number[]) => {
      if (!outline) return
      const updatedItems = addSubChapterToPath(outline, path)
      onOutlineUpdate(updatedItems)
    },
    [outline, onOutlineUpdate],
  )

  return {
    expandedItems,
    showDescriptions,
    toggleExpand,
    toggleExpandAll,
    toggleShowDescriptions,
    updateOutlineItem,
    deleteChapter,
    addChapter,
    addSubChapter,
    collectAllSequences,
  }
}
