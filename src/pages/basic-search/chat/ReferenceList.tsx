import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { ReferenceItem } from '@/api/getSession'
import { cn } from '@/lib/utils'

export interface ReferenceList {
  referenceList?: ReferenceItem[]
  className?: string
}

const ReferenceList: React.FC<ReferenceList> = ({ referenceList, className }: ReferenceList) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)

  const handleClick = (url: string) => {
    window.open(url, '_blank')
  }
  return (
    <>
      {referenceList && referenceList.length > 0 && (
        <>
          <br />
          <Text className={cn('my-1', className)} type={TextEnum.H5}>
            {t(`chat.refer`)}
          </Text>
          {referenceList.map((item, idx) => (
            <div key={idx} className='break-all'>
              <Text className='mr-2 inline leading-4' type={TextEnum.Body_big}>
                {idx + 1}.&nbsp;&nbsp;{item?.title}
              </Text>
              <Text
                onClick={() => {
                  handleClick(item.url)
                }}
                type={TextEnum.Body_medium}
                className='inline cursor-pointer break-words leading-4 text-primary'>
                {item.url}
              </Text>
            </div>
          ))}
        </>
      )}
    </>
  )
}

export default ReferenceList
