import { SearchInput } from '@/components/business/search-input'
import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { SearchModeEnum } from '@/types'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export interface ChatFooterProps {
  type: string
  onSearch: (query: string, mode: SearchModeEnum) => void
  onStop: () => void
  showStop: boolean
  disabled: boolean
}

const ChatFooter: React.FC<ChatFooterProps> = ({
  onSearch,
  onStop,
  showStop,
  disabled,
}: ChatFooterProps) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const [inputValue, setInputValue] = useState('')
  const [searchMode, setSearchMode] = useState<SearchModeEnum>(SearchModeEnum.AUTO)

  const handleSearch = (value: string, mode: SearchModeEnum) => {
    setInputValue('')
    if (onSearch) {
      onSearch(value, mode)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)
  }

  return (
    <div className='z-10 flex justify-center gap-2 bg-background px-14 py-2.5'>
      <div className='ml-16 min-w-[600px] max-w-[760px] flex-1'>
        <SearchInput
          placeholder={t('chat.input.placehoder')}
          maxLength={5000}
          className='flex-1 shrink-0'
          showStop={showStop}
          disabled={disabled}
          value={inputValue}
          onChange={handleChange}
          onEnter={handleSearch}
          onStop={onStop}
          searchMode={searchMode}
          onChangeSearchMode={(mode: SearchModeEnum) => {
            setSearchMode(mode)
          }}
          hideDeepResearch={true} // 添加这个属性来隐藏Deep Research按钮
        />
        <Text className='mt-2.5 text-center text-secondary-black-3' type={TextEnum.Body_small}>
          {t(`homePage.tip`)}
        </Text>
      </div>
      {<div className='w-[344px] shrink-0'></div>}
    </div>
  )
}

export default ChatFooter
