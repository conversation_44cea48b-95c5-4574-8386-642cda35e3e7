import React from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

interface ResearchStatusIndicatorProps {
  researchStatus: string
}

const ResearchStatusIndicator: React.FC<ResearchStatusIndicatorProps> = ({ researchStatus }) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)

  // 尝试获取翻译文本，如果不存在则使用进行中
  const statusText = t(`researchResults.status.${researchStatus}`, {
    defaultValue: t('researchResults.status.inProgress'),
  })
  // 根据状态设置颜色和动画
  let statusColor = 'bg-indigo-100 text-indigo-800'
  let isAnimated = true

  if (researchStatus === 'END') {
    statusColor = 'bg-green-100 text-green-800'
    isAnimated = false // 完成状态不需要动画
  } else if (researchStatus === 'START') {
    statusColor = 'bg-blue-100 text-blue-800'
  } else if (researchStatus === 'PLANNING') {
    statusColor = 'bg-purple-100 text-purple-800'
  } else if (researchStatus === 'RESEARCHING') {
    statusColor = 'bg-yellow-100 text-yellow-800'
  } else if (researchStatus === 'DRAFTING') {
    statusColor = 'bg-orange-100 text-orange-800'
  } else if (researchStatus === 'REFLECTING') {
    statusColor = 'bg-teal-100 text-teal-800'
  } else if (researchStatus === 'REPORTING') {
    statusColor = 'bg-pink-100 text-pink-800'
  } else if (researchStatus === 'FAILED') {
    statusColor = 'bg-red-100 text-red-800'
    isAnimated = false // 完成状态不需要动画
  }

  return (
    <div
      className={`inline-flex h-6 items-center rounded-full px-3 py-1 text-[12px] font-medium ${statusColor} ${
        isAnimated ? 'animate-pulse' : ''
      }`}>
      {isAnimated && (
        <div className='relative mr-2'>
          <div className='h-2 w-2 rounded-full bg-current opacity-75'></div>
          <div className='absolute left-0 top-0 h-2 w-2 animate-ping rounded-full bg-current opacity-75'></div>
        </div>
      )}
      {statusText}
    </div>
  )
}

export default ResearchStatusIndicator
