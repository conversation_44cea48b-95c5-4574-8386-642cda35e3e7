import React from 'react'
import { ChapterProgress } from '../hooks/useResearchProgress'

interface ProgressDisplayProps {
  progress: ChapterProgress
  className?: string
}

const ProgressDisplay: React.FC<ProgressDisplayProps> = ({ progress, className = '' }) => {
  return (
    <span className={`flex items-center gap-1 text-sm font-normal text-primary ${className}`}>
      <span>
        {progress.completed}/{progress.total}
      </span>
      <span className='text-xs text-gray-500'>({progress.percentage}%)</span>
    </span>
  )
}

export default ProgressDisplay
