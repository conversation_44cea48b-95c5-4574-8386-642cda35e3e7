import { BellAlertIcon } from '@heroicons/react/24/outline'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Mail } from 'lucide-react'

interface NotificationDropdownProps {
  className?: string
  onEmailNotificationToggle?: (enabled: boolean) => void
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  className = '',
  // onEmailNotificationToggle,
}) => {
  // const [emailNotificationEnabled, setEmailNotificationEnabled] = useState(false)

  // const handleEmailNotificationToggle = (checked: boolean) => {
  //   setEmailNotificationEnabled(checked)
  //   onEmailNotificationToggle?.(checked)
  // }

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className='flex items-center gap-1 rounded-full px-4 py-2 text-sm text-primary outline-none'>
            <BellAlertIcon className='h-4 w-4'></BellAlertIcon>
            <span>通知我</span>
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align='end' className='w-64'>
          <DropdownMenuItem className='flex items-center gap-3 px-3 py-3'>
            <div className='flex h-8 w-8 items-center justify-center rounded-full bg-green-100'>
              <Mail className='h-4 w-4 text-green-600' />
            </div>
            <div className='flex-1'>
              <div className='text-sm font-medium'>通过邮件通知</div>
              <div className='text-xs text-muted-foreground'>研究完成后发送邮件提醒</div>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export default NotificationDropdown
