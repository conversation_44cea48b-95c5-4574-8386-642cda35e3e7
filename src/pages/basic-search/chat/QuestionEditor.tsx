import { PoTypeEnum } from '@/types'
import { PencilSquareIcon } from '@heroicons/react/24/outline'
import { Text, TextEnum } from '@/components/business/text'
import clsx from 'clsx'
import { useEffect, useRef, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { MessageKey, MessageType } from '@/types'
import { cloneDeep, trim } from 'lodash'
import { Editor } from './Editor'
import React, { useMemo } from 'react'

export interface QuestionEditorProps {
  type: PoTypeEnum
  disabled?: boolean
  messages: MessageType
  onChange?: (type: PoTypeEnum, messages: MessageType) => void
  onCancel?: () => void
  onSubmit?: () => void
}

export const QuestionEditor = React.forwardRef<HTMLDivElement, QuestionEditorProps>(
  (props, ref) => {
    const { type, disabled = false, messages, onChange, onCancel, onSubmit } = props

    const { t } = useTranslation([Namespace.BASIC_SEARCH, Namespace.GLOBAL])
    const [isEditMode, setIsEditMode] = useState<boolean>(false)
    const [disabledSubmit, setDisabledSubmit] = useState<boolean>(true)
    const [editIconVisible, setEditIconVisible] = useState<boolean>(false)
    const inputValueRef = useRef<HTMLDivElement | null>(null)
    const [questionFilledValue, setQuestionFilledValue] = useState<
      Record<PoTypeEnum, Array<{ title?: string; value: string; key: MessageKey }>>
    >({
      GENERAL: [],
      MARKET: [],
      TOPIC: [],
      SWOT: [],
      COMPANY: [],
      REGULATION: [],
      RISK: [],
    })

    const questions = useMemo(
      () => ({
        GENERAL: [{ title: t('general.query'), value: '', key: 'query' }],
        MARKET: [
          { title: t('market.query1'), value: '', key: 'description' },
          { title: t('market.query2'), value: '', key: 'targetMarket' },
        ],
        TOPIC: [
          { title: t('topic.query1'), value: '', key: 'topic' },
          { title: t('topic.query2'), value: '', key: 'details' },
        ],
        SWOT: [{ title: t('swot.query1'), value: '', key: 'description' }],
        COMPANY: [{ title: t('company.query1'), value: '', key: 'companyName' }],
        REGULATION: [
          { title: t('oversea.query1'), value: '', key: 'description' },
          { title: t('oversea.query2'), value: '', key: 'targetMarket' },
          { title: t('oversea.query3'), value: '', key: 'currentLocation' },
        ],
        RISK: [{ title: t('risk.query1'), value: '', key: 'description' }],
      }),
      [t],
    ) // ✅ 监听 t

    useEffect(() => {
      const newQuestions = questions[type.toUpperCase() as PoTypeEnum].map((item, index) => {
        const findObj = messages.find((message) => message.key === item.key)
        if (findObj)
          return {
            ...item,
            value: findObj?.value ?? '',
          }
        else {
          // 意图分析过来的，直接取一个 query
          if (index === 0)
            return {
              ...item,
              value: messages[0].value ?? '',
            }
          return {
            ...item,
          }
        }
      })
      setQuestionFilledValue((prev) => ({
        ...prev,
        [type.toUpperCase() as PoTypeEnum]: newQuestions,
      }))
    }, [type, JSON.stringify(messages), questions]) // 深度比较 messages

    const handleEdit = () => {
      if (disabled) return
      setIsEditMode(true)
    }

    const handleCancel = () => {
      if (onCancel) onCancel()
      setIsEditMode(false)
    }

    const handleSubmit = () => {
      if (onSubmit) onSubmit()
      setIsEditMode(false)
    }

    useEffect(() => {
      const checked = messages.every((item) => {
        return Boolean(trim(item.value))
      })
      setDisabledSubmit(!checked)
    }, [JSON.stringify(messages)])

    const handleQuestionChange = (type: PoTypeEnum, key: MessageKey, value: string) => {
      const clonedMessages: MessageType = cloneDeep(messages)
      const newMessages = clonedMessages.map((item) => {
        if (item.key === key) {
          return {
            ...item,
            value,
          }
        }
        return item
      })
      if (onChange) {
        onChange(type, newMessages as MessageType)
      }
    }

    const handleEnter = () => {
      if (!disabledSubmit) {
        handleSubmit()
      }
    }
    return (
      <div className='flex'>
        <div className='flex flex-1 flex-col gap-2 font-sans' ref={ref}>
          {questionFilledValue &&
            questionFilledValue[type.toUpperCase() as PoTypeEnum].map((item, index) => {
              return (
                <div key={index}>
                  {((!isEditMode && item.value) || isEditMode) && (
                    <Text
                      type={TextEnum.H5}
                      className={clsx('mt-2 overflow-hidden text-ellipsis whitespace-nowrap')}>
                      {item && item?.title}
                    </Text>
                  )}

                  {isEditMode ? (
                    <Editor
                      withIcon={false}
                      maxLength={
                        item.key === 'targetMarket' ||
                        item.key === 'companyName' ||
                        item.key === 'currentLocation'
                          ? 300
                          : 5000
                      }
                      value={item?.value ?? ''}
                      onChange={(e) => {
                        handleQuestionChange(type, item.key, e.target.value)
                      }}
                      style={{ width: inputValueRef.current?.offsetWidth + 'px' }}
                      onEnter={handleEnter}
                    />
                  ) : (
                    item?.value && (
                      <div
                        ref={inputValueRef}
                        className='relative mt-2 flex min-h-10 items-center break-all rounded-sm bg-secondary px-5 py-2'
                        onMouseEnter={() => {
                          setEditIconVisible(true)
                        }}
                        onMouseLeave={() => {
                          setEditIconVisible(false)
                        }}>
                        <div className='flex items-center'>{item?.value}</div>
                        {editIconVisible && (
                          <PencilSquareIcon
                            className={clsx(
                              'absolute right-1 top-1/2 h-4 w-4 shrink-0 -translate-y-2 cursor-pointer',
                              disabled ? 'text-muted-foreground' : 'text-foreground',
                            )}
                            onClick={handleEdit}
                          />
                        )}
                      </div>
                    )
                  )}
                </div>
              )
            })}
          {isEditMode ? (
            <div className='mt-2.5 flex justify-end'>
              <Button variant='outline' className='mr-2.5 border-border' onClick={handleCancel}>
                {t('global:button.cancel')}
              </Button>
              <Button onClick={handleSubmit} disabled={disabledSubmit}>
                {t('global:button.submit')}
              </Button>
            </div>
          ) : null}
        </div>
      </div>
    )
  },
)

QuestionEditor.displayName = 'QuestionEditor'
