import { useState, useRef } from 'react'

// 定义大纲项的类型
export interface OutlineItem {
  id: string
  title: string
  description: string
  chapterIndex: string
  level: string
  children: OutlineItem[]
}

// 定义研究消息类型
export interface ResearchMessage {
  ROLE: string
  TYPE: string
  STEP: string
  CONTENT: {
    ACTION?: string
    CHAPTER?: string
    MESSAGE?: string
  }
}

// 定义研究数据类型
export interface ResearchData {
  task_id: string
  question: string
  requirement: string
}

// 定义章节内容项类型
export interface ChapterContentItem {
  type: 'message' | 'markdown'
  message?: ResearchMessage
  content?: string
  isStreaming?: boolean
}

export const useResearchState = () => {
  // 基础状态
  const [isGlobalLoading, setIsGlobalLoading] = useState<boolean>(false)
  const [researchStatus, setResearchStatus] = useState<string>('START')
  const [streamingComplete, setStreamingComplete] = useState<boolean>(false)
  const [isFailed, setIsFailed] = useState<boolean>(false)
  const [showToast, setShowToast] = useState<boolean>(false)
  const [isRetrying, setIsRetrying] = useState<boolean>(false) // 新增：是否正在重试

  // 消息和内容状态
  const [researchMessages, setResearchMessages] = useState<ResearchMessage[]>([])
  const [chapterContentItems, setChapterContentItems] = useState<Map<string, ChapterContentItem[]>>(
    new Map(),
  )
  const [outline, setOutline] = useState<OutlineItem[]>([])

  // Refs
  const researchDataRef = useRef<ResearchData | null>(null)
  const outlineRef = useRef<OutlineItem[]>([])
  const streamRequestSentRef = useRef<boolean>(false)

  // 设置研究数据
  const setResearchData = (data: ResearchData) => {
    researchDataRef.current = data
  }

  // 设置大纲数据
  const setOutlineData = (outlineData: OutlineItem[]) => {
    setOutline(outlineData)
    outlineRef.current = outlineData
  }

  // 添加研究消息（带去重）
  const addResearchMessage = (message: ResearchMessage) => {
    setResearchMessages((prev) => {
      // 对于 REPORTING 类型的消息，使用更严格的去重逻辑
      if (message.ROLE === 'RESEARCHER' && message.TYPE === 'REPORTING') {
        // 检查是否已存在相同章节的相同步骤的 REPORTING 消息
        const isDuplicate = prev.some((existingMsg) => {
          return (
            existingMsg.ROLE === 'RESEARCHER' &&
            existingMsg.TYPE === 'REPORTING' &&
            existingMsg.STEP === message.STEP &&
            existingMsg.CONTENT?.CHAPTER === message.CONTENT?.CHAPTER &&
            existingMsg.CONTENT?.MESSAGE === message.CONTENT?.MESSAGE
          )
        })

        if (isDuplicate) {
          console.log('🚫 Duplicate REPORTING message detected, skipping:', {
            ROLE: message.ROLE,
            TYPE: message.TYPE,
            STEP: message.STEP,
            CHAPTER: message.CONTENT?.CHAPTER,
            messageLength: message.CONTENT?.MESSAGE?.length || 0,
          })
          return prev
        }
      } else {
        // 对于其他类型的消息，使用基本的去重逻辑
        const isDuplicate = prev.some((existingMsg) => {
          return (
            existingMsg.ROLE === message.ROLE &&
            existingMsg.TYPE === message.TYPE &&
            existingMsg.STEP === message.STEP &&
            existingMsg.CONTENT?.CHAPTER === message.CONTENT?.CHAPTER &&
            existingMsg.CONTENT?.ACTION === message.CONTENT?.ACTION
          )
        })

        if (isDuplicate) {
          console.log('🚫 Duplicate message detected, skipping:', {
            ROLE: message.ROLE,
            TYPE: message.TYPE,
            STEP: message.STEP,
            CHAPTER: message.CONTENT?.CHAPTER,
          })
          return prev
        }
      }

      console.log('✅ Adding new unique message:', {
        ROLE: message.ROLE,
        TYPE: message.TYPE,
        STEP: message.STEP,
        CHAPTER: message.CONTENT?.CHAPTER,
        messageLength: message.CONTENT?.MESSAGE?.length || 0,
      })

      return [...prev, message]
    })
  }

  // 批量添加研究消息（用于历史数据）
  const addResearchMessages = (messages: ResearchMessage[]) => {
    setResearchMessages((prev) => [...prev, ...messages])
  }

  // 重置状态
  const resetState = () => {
    setIsGlobalLoading(false)
    setResearchStatus('START')
    setStreamingComplete(false)
    setIsFailed(false)
    setShowToast(false)
    setResearchMessages([])
    setChapterContentItems(new Map())
    setOutline([])
    researchDataRef.current = null
    outlineRef.current = []
    streamRequestSentRef.current = false
  }

  return {
    // 状态
    isGlobalLoading,
    setIsGlobalLoading,
    researchStatus,
    setResearchStatus,
    streamingComplete,
    setStreamingComplete,
    isFailed,
    setIsFailed,
    showToast,
    setShowToast,
    isRetrying,
    setIsRetrying,
    researchMessages,
    chapterContentItems,
    setChapterContentItems,
    outline,

    // Refs
    researchDataRef,
    outlineRef,
    streamRequestSentRef,

    // 方法
    setResearchData,
    setOutlineData,
    addResearchMessage,
    addResearchMessages,
    resetState,
  }
}
