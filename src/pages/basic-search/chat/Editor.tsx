import { Textinput, TextinputProps } from '@/components/business/text-input'
import { useEffect, useRef, useState } from 'react'

export const Editor: React.FC<TextinputProps> = ({ ...props }: TextinputProps) => {
  const textRef = useRef<HTMLTextAreaElement | null>(null)

  const { onChange, onFocus, onBlur, value, ...attrs } = props
  const [inputValue, setInputValue] = useState(props.value || props.defaultValue || '')

  useEffect(() => {
    if ('value' in props) {
      setInputValue(value || '')
    }
  }, [value])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)
    if (onChange) {
      onChange(e)
    }
  }

  return (
    <Textinput
      {...attrs}
      withIcon={false}
      value={inputValue}
      ref={textRef}
      onFocus={(e) => {
        onFocus && onFocus(e)
      }}
      onBlur={(e) => {
        onBlur && onBlur(e)
      }}
      onChange={handleChange}
    />
  )
}
