import { CheckIcon } from '@heroicons/react/24/outline'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { PoTypeEnum, ProcessStatusEnum } from '@/types'

import { ProcessType } from '@/api/getInProgressTask'
import LoadingDots from '@/components/business/loading-dots'
import { TextEnum, Text } from '@/components/business/text'
import clsx from 'clsx'

export interface InProgressProcessProps {
  process: ProcessType
  className?: string
  isFirstQuery: boolean
}

const hasAllSteps = (poType?: PoTypeEnum) => {
  if (!poType) return false
  if (poType === PoTypeEnum.SWOT || poType === PoTypeEnum.RISK) {
    return false
  }
  return true
}

export const InProgressProcess: React.FC<InProgressProcessProps> = ({
  process,
  className,
  isFirstQuery,
}: InProgressProcessProps) => {
  const { t } = useTranslation([Namespace.BASIC_SEARCH])
  const { status, poType } = process
  if (!isFirstQuery) {
    switch (status) {
      case ProcessStatusEnum.STARTING:
      case ProcessStatusEnum.FINISH_USERINTENT:
        return (
          <div className={clsx('flex flex-col gap-4 text-secondary-foreground', className)}>
            <div className='mt-1 flex items-center gap-2'>
              <LoadingDots size={4} />
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.summarize')} </Text>
            </div>
          </div>
        )
      default: // finsh canceled failed 不需要展示 直接展示聊天记录了
        return null
    }
  } else {
    switch (status) {
      case ProcessStatusEnum.STARTING:
      case ProcessStatusEnum.FINISH_USERINTENT:
        return (
          <div className={clsx('flex flex-col gap-4 text-secondary-foreground', className)}>
            {hasAllSteps(poType) && (
              <div className='flex items-center gap-2'>
                <LoadingDots size={4} />
                <Text type={TextEnum.Body_medium}> {t('chat.statustext.search')} </Text>
              </div>
            )}
            {hasAllSteps(poType) && (
              <div className='flex items-center gap-2'>
                <LoadingDots size={4} />
                <Text type={TextEnum.Body_medium}> {t('chat.statustext.extract')} </Text>
              </div>
            )}
            <div className='flex items-center gap-2'>
              <LoadingDots size={4} />
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.summarize')} </Text>
            </div>
          </div>
        )
      case ProcessStatusEnum.FINISH_SEARCH:
        return (
          <div className={clsx('flex flex-col gap-4 text-secondary-foreground', className)}>
            <div className='flex items-center gap-2'>
              <CheckIcon width={20} className='text-success'></CheckIcon>
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.search')} </Text>
            </div>
            <div className='flex items-center gap-2'>
              <LoadingDots size={4} />
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.extract')} </Text>
            </div>
            <div className='flex items-center gap-2'>
              <LoadingDots size={4} />
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.summarize')} </Text>
            </div>
          </div>
        )
      case ProcessStatusEnum.FINISH_WEBCRAWLER:
        return (
          <div className={clsx('flex flex-col gap-4 text-secondary-foreground', className)}>
            <div className='flex items-center gap-2'>
              <CheckIcon width={20} className='text-success'></CheckIcon>
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.search')} </Text>
            </div>
            <div className='flex items-center gap-2'>
              <CheckIcon width={20} className='text-success'></CheckIcon>
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.extract')} </Text>
            </div>
            <div className='flex items-center gap-2'>
              <LoadingDots size={4} />
              <Text type={TextEnum.Body_medium}> {t('chat.statustext.summarize')} </Text>
            </div>
          </div>
        )
      default: // finsh canceled failed 不需要展示 直接展示聊天记录了
        return null
    }
  }
}
