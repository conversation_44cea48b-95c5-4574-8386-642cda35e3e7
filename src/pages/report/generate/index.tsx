import Layout from '@/components/business/layout/Layout'
import { RightResource } from '../components/right-sidebar/RightResource'
import { Catalogue } from '../components/left-sidebar/Catalogue'
import { DocumentIndex } from '../components/doc-index/DocumentIndex'
import { useEffect } from 'react'
import { useRouter } from 'next/router'
import { useReport } from '../store/useReport'
import { useReportList } from '../store/useReportList'
const Generate = () => {
  const router = useRouter()
  const getReportById = useReport((s) => s.getReportById)
  const getReportList = useReportList((s) => s.getReportList)
  useEffect(() => {
    const { reportId } = router.query
    // 获取当前文档详情
    getReportById(reportId as string)
    // 刷新需要重新获取列表
    getReportList()
  }, [])

  return (
    <Layout
      tabName='generator'
      showRightSideBar
      // 右边栏
      sideBarRightContent={<RightResource />}
      // 左边栏
      sideBarContent={<Catalogue />}>
      <div className='flex h-screen-minus-header flex-col justify-between overflow-auto bg-card'>
        <DocumentIndex />
      </div>
    </Layout>
  )
}

export default Generate
