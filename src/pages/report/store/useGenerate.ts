import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import {
  updateDocument<PERSON><PERSON>,
  reportGenerate<PERSON><PERSON>,
  ReportTypeEnum,
  reportCancelGenerateApi,
} from '@/api/document/index'

interface UpdateType {
  reportId: string
  title?: string
  description?: string
  type?: ReportTypeEnum
}
interface GenerateStore {
  isSetDescription: boolean
  isCancelled: boolean
  updateDocument: (data: UpdateType) => Promise<void>
  generateDocument: (reportId: string) => Promise<void>
  cancelGenerateDocument: (reportId: string) => Promise<void>
  setDescriptionLoading: (status: boolean) => void
  reportProcessId: string
  reportIdMap: { [reportId: string]: string }
  setReportIdMap: (reportId: string, content: string) => void
}
export const useGenerate = create<GenerateStore>()(
  immer((set) => ({
    isSetDescription: false,
    isCancelled: false,
    reportProcessId: '',
    reportIdMap: {},
    updateDocument: async (data) => {
      try {
        await updateDocumentApi(data)
      } catch (error) {
        // set({ isSetDescription: false })
      }
    },
    setDescriptionLoading: (status) => {
      set({ isSetDescription: status })
    },
    generateDocument: async (reportId) => {
      set({ isCancelled: false })
      try {
        const res = await reportGenerateApi({ reportId })
        set({ reportProcessId: res.reportProcessId })
      } catch (error) {
        console.error('文档生成失败', error)
      }
    },
    cancelGenerateDocument: async (reportId) => {
      try {
        await reportCancelGenerateApi({ reportId })
        set({ isCancelled: true })
        set({ reportProcessId: '' })
        set({
          reportIdMap: {
            [reportId]: '',
          },
        })
      } catch (error) {
        console.error('文档��销生成失败', error)
        set({ isCancelled: false })
      }
    },
    setReportIdMap: (reportId, content) => {
      set({
        reportIdMap: {
          [reportId]: content,
        },
      })
    },
  })),
)
