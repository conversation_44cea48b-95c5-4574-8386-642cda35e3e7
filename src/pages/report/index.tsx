import Layout from '@/components/business/layout/Layout'
import { Text, TextEnum } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import { PlusIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { SkeletonCard } from './components/doc-index/DocSkeleton'
import { useReport } from './store/useReport'
import { useReportList } from './store/useReportList'
const Report = () => {
  const { t } = useTranslation(Namespace.GENERATOR)
  const router = useRouter()
  const getReportList = useReportList((state) => state.getReportList)
  const createReport = useReport((state) => state.createReport)
  const [isLoading, setLoading] = useState(true)

  const onCreateReport = async () => {
    const res = await createReport()
    toGenerateReport(res.reportId)
  }
  const getReportListPage = async () => {
    const res = await getReportList()
    if (res.length) {
      // 跳转到文档生成页，默认激活列表第一条数据
      const reportId = res[0].reportId
      toGenerateReport(reportId)
    } else {
      setLoading(false)
    }
  }
  useEffect(() => {
    // Tracking.trackPageView({ eventName: 'VISIT_REPORT_PAGE' })
    setLoading(true)
    getReportListPage()
  }, [])

  const toGenerateReport = (reportId: string) => {
    router.push({
      pathname: smartiesRoutes.document.generate,
      query: { reportId },
    })
  }

  return (
    <div
      className='h-screen min-w-[840px] bg-background'
      asm-tracking='TEST_VISIT_REPORT_PAGE:VIEW'>
      <Layout showSideBar={false} tabName='generator'>
        <div className='flex-center h-screen-minus-header flex-col'>
          {isLoading ? (
            <SkeletonCard />
          ) : (
            <>
              <Image src='/images/pdf-bg.png' alt='' width={192} height={173} />
              <Button
                className='mb-6 mt-[100px] h-9 w-[196px] cursor-pointer rounded-sm'
                onClick={onCreateReport}>
                <Text type={TextEnum.Body_medium} className='flex items-center'>
                  <PlusIcon className='mr-1 h-4 w-4'></PlusIcon>
                  {t('createDoc')}
                </Text>
              </Button>
            </>
          )}
        </div>
      </Layout>
    </div>
  )
}

export default Report
