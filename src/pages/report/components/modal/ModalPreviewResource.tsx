import { Modal } from '@/components/business/modal'
import { Text, TextEnum } from '@/components/business/text'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { ChatPreview } from '../source-list/chat/ChatPreview'
import { MediaPreview } from '../source-list/media/MediaPreview'
import { PdfPreviews } from '../source-list/pdf/PdfPreview'
export const ModalPreviewResource = ({ open, onClose, resource }) => {
  const render = () => {
    if (resource.type === 'CHAT') {
      return <ChatPreview chat={resource} />
    } else if (resource.type === 'MEDIA') {
      return <MediaPreview media={resource} />
    } else if (resource.type === 'PDF') {
      return <PdfPreviews pdf={resource} />
    }
    return <Text type={TextEnum.P}>No preview available.</Text>
  }
  return (
    <Modal open={open} onClose={onClose}>
      <div className='h-screen min-h-96 w-screen min-w-[700px] bg-primary-bg'>
        <Text
          type={TextEnum.H4}
          className='flex h-16 items-center justify-between border-b-2 border-border bg-card px-8'>
          <span>{resource.title || ''}</span>
          <XMarkIcon onClick={onClose} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div>{render()}</div>
      </div>
    </Modal>
  )
}
