import { Modal } from '@/components/business/modal'
import { But<PERSON> } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { Checkbox } from '@/components/ui/checkbox'
import { useState } from 'react'

interface ModalDeductProps {
  open: boolean
  handleCancel: () => void
  onConfirm: (showAgain: boolean) => void
}

export const ModalDeduct = ({ open, handleCancel, onConfirm }: ModalDeductProps) => {
  const { t } = useTranslation(Namespace.GENERATOR)
  const [check, setCheck] = useState(false)
  const onCheckedChange = (checked: boolean) => {
    setCheck(checked)
  }

  return (
    <Modal
      open={open}
      className='flex h-[208px] w-[434px] flex-col items-center justify-between p-6'>
      <div className='flex h-[56px] w-[386px] items-center justify-between'>
        <Text
          type={TextEnum.H4}
          className='text-left font-roboto text-[18px] font-semibold leading-[28px] tracking-[0%] text-[rgba(9,9,11,1)]'>
          {t('deepResearchDeductTitle')}
        </Text>
        <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
      </div>
      <div className='mt-5 h-[24px] w-[386px]'>
        <div className='text-left font-roboto text-[16px] font-normal leading-[24px] text-[rgba(9,9,11,1)]'>
          {t('deepResearchPromptContent')}
        </div>
      </div>
      <div className='flex h-[40px] w-[386px] flex-row items-center justify-between'>
        <div className='flex items-center whitespace-nowrap'>
          <Checkbox checked={check} onCheckedChange={onCheckedChange} />
          <Text type={TextEnum.Body_medium} className='ml-2'>
            {t('showAgain')}
          </Text>
        </div>
        <div className='flex gap-2'>
          <Button variant='secondary' onClick={handleCancel}>
            {t('cancel')}
          </Button>
          <Button
            onClick={() => {
              onConfirm(check)
            }}>
            {t('confirm')}
          </Button>
        </div>
      </div>
    </Modal>
  )
}
