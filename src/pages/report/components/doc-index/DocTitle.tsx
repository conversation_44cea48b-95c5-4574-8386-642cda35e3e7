import { Text, TextEnum } from '@/components/business/text'
import { PencilSquareIcon } from '@heroicons/react/24/outline'
import { Textinput } from '@/components/business/text-input'
import { useState } from 'react'
import { useReport } from '../../store/useReport'
import { useGenerate } from '../../store/useGenerate'
import { useReportList } from '../../store/useReportList'
import { DocBtn } from './DocBtn'
export const DocTitle = () => {
  const currentReport = useReport((s) => s.currentReport)

  const updateCurrentReport = useReport((s) => s.updateCurrentReport)
  const updateDocument = useGenerate((s) => s.updateDocument)
  const updateReportList = useReportList((s) => s.updateReportList)
  const reportList = useReportList((s) => s.reportList)
  const [inputValue, setInputValue] = useState(currentReport.title)
  const [isClickTitle, setIsClickTitle] = useState(false)
  const [isShowEdit, setIsShowEdit] = useState(false)
  const delayedSetTitle = async (title) => {
    const reportId = currentReport.reportId
    await updateDocument({ reportId, title })
    // 更新当前标题，和左侧列表的标题
    updateCurrentReport({ title })
    const list = reportList.map((r) => (r.reportId === reportId ? { ...r, title } : r))
    updateReportList(list)
  }

  const onChangeTextInput = (e) => {
    setInputValue(e.target.value)
    // if (e.target.value) {
    //   delayedSetTitle(e.target.value)
    // }
  }
  const editTitle = () => {
    setIsClickTitle(true)
  }
  const onTitleBlur = (e) => {
    setIsClickTitle(false)
    setIsShowEdit(false)
    delayedSetTitle(e.target.value)
  }

  // 定位光标到末尾
  const onTitleFocus = (e) => {
    const len = e.target.value.length
    e.target.selectionStart = len
    e.target.selectionEnd = len
  }

  return (
    <div className='flex justify-between'>
      <div className='mr-4 flex-1'>
        {isClickTitle ? (
          <div className='pr-4'>
            <Textinput
              maxLength={100}
              isStopEnterPrevent={false}
              className='border-none pt-0.5 text-[32px] font-bold leading-snug ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0'
              onFocus={onTitleFocus}
              autoFocus
              value={inputValue}
              onBlur={onTitleBlur}
              onChange={onChangeTextInput}
            />
          </div>
        ) : (
          <Text
            type={TextEnum.H2}
            className='pr-6'
            onMouseEnter={() => {
              setIsShowEdit(true)
            }}
            onMouseLeave={() => {
              setIsShowEdit(false)
            }}>
            <div className='relative mr-3 cursor-pointer' onClick={editTitle}>
              {currentReport.title}
              {isShowEdit && (
                <PencilSquareIcon
                  onClick={editTitle}
                  className='absolute -right-12 top-3 h-6 w-6 shrink-0 cursor-pointer'></PencilSquareIcon>
              )}
            </div>
          </Text>
        )}
      </div>
      <div className='relative flex'>{currentReport.type && <DocBtn></DocBtn>}</div>
    </div>
  )
}
