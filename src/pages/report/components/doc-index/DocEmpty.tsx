import { Text, TextEnum } from '@/components/business/text'
import { useDocEmpty } from '../../hooks/useDocEmpty'
import { ModalDesc } from '../modal/ModalDesc'
import { useState } from 'react'
export const DocEmpty = ({ onSetModal }) => {
  const [open, setOpen] = useState(false)
  const [descModalInfo, setDescModalInfo] = useState({})
  const docList = useDocEmpty('')
  const onOpenDialog = (info) => {
    if (info.key === 'set') {
      onSetModal()
    } else {
      setOpen(true)
      setDescModalInfo(info)
    }
  }
  const closeModalDesc = () => {
    setOpen(false)
  }
  return (
    <ul className='mt-9'>
      {docList.map((doc) => {
        return (
          <li className='mt-1 flex items-center p-2 pl-0' key={doc.key}>
            {doc.render()}
            <Text type={TextEnum.Body_medium} className='text-secondary-black-3'>
              <span
                className='cursor-pointer hover:text-primary'
                onClick={() => {
                  onOpenDialog(doc)
                }}>
                {doc.name}
              </span>
            </Text>
          </li>
        )
      })}
      <ModalDesc open={open} handleCancel={closeModalDesc} info={descModalInfo}></ModalDesc>
    </ul>
  )
}
