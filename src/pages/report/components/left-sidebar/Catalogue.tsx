import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import clsx from 'clsx'
import { Text, TextEnum } from '@/components/business/text'
import { PlusIcon, DocumentTextIcon, TrashIcon } from '@heroicons/react/24/outline'
import { DocSkeleton } from '../doc-index/DocSkeleton'
import { useRef, useState } from 'react'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import ModalDelete from '../modal/ModalDelete'
import { useReportList } from '../../store/useReportList'
import { useReport } from '../../store/useReport'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'

export const Catalogue = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()
  const reportList = useReportList((s) => s.reportList)
  const updateReportList = useReportList((s) => s.updateReportList)
  const currentReport = useReport((s) => s.currentReport)
  const updateCurrentReport = useReport((s) => s.updateCurrentReport)
  const createReport = useReport((s) => s.createReport)
  const getReportById = useReport((s) => s.getReportById)
  const deleteReportById = useReport((s) => s.deleteReportById)
  const [createLoading, setCreateLoading] = useState(false)
  // 创建文档，同时更新列表，更新当前 router 的 query
  const onCreateReport = async () => {
    setCreateLoading(true)
    const { reportId } = await createReport()
    // 获取当前文档详情
    const res = await getReportById(reportId)
    // 更新当前 router 的 query
    updateRouterQuery(reportId, false)
    const list = JSON.parse(JSON.stringify(reportList))
    list.unshift(res)
    updateReportList(list)
    setCreateLoading(false)
  }
  const updateRouterQuery = async (reportId: string, requestReport = true) => {
    if (requestReport) {
      await getReportById(reportId)
    }
    router.push({
      pathname: smartiesRoutes.document.generate,
      query: { reportId },
    })
  }
  const [deleteReportId, setDeleteReportId] = useState(null)
  const [deleteIndex, setDeleteIndex] = useState(null)
  const [visibleDelete, setVisibleDelete] = useState(null)
  const clickReportId = useRef('')
  const onDeleteCatalog = (item) => {
    setVisibleDelete(true)
    clickReportId.current = item.reportId
  }

  const onDeleteConfirm = async () => {
    setVisibleDelete(false)
    const index = reportList.findIndex((r) => r.reportId === clickReportId.current)
    setDeleteIndex(index)
    await deleteReportById(clickReportId.current)
    reportList.splice(index, 1)
    if (reportList.length === 0) {
      router.push({
        pathname: smartiesRoutes.document.home,
      })
    }
    const reportItem = reportList[0]
    if (clickReportId.current === currentReport.reportId && reportItem) {
      updateCurrentReport(reportItem)
      updateRouterQuery(reportItem.reportId)
    }
    updateReportList(reportList)
    setTimeout(() => {
      setDeleteIndex(null)
    }, 300)
  }
  return (
    <div className='hide-scrollbar h-screen-minus-header overflow-scroll px-4'>
      <div className='mb-4 mt-4 pb-4'>
        <div
          className='flex-center h-9 cursor-pointer rounded-sm border-2 text-foreground hover:bg-secondary-hover'
          onClick={onCreateReport}>
          <PlusIcon className='mr-1 h-4 w-4' />
          <Text type={TextEnum.Body_medium}>{t('button.newDocument')}</Text>
        </div>
        <ul className='mt-6'>
          {reportList.length === 0 && (
            <DocSkeleton rows={6} className='h-7 w-72 rounded-md'></DocSkeleton>
          )}
          {reportList.map((item, index) => (
            <li
              key={item.reportId}
              className={clsx(
                'relative mb-1 flex cursor-pointer items-center rounded-md p-3 hover:bg-primary-bg',
                // 设置激活标题
                currentReport?.reportId === item?.reportId ? 'bg-primary-bg' : '',
                deleteIndex === index ? 'fadeOut' : '',
              )}
              onMouseEnter={() => {
                setDeleteReportId(item.reportId)
              }}
              onMouseLeave={() => {
                setDeleteReportId(null)
              }}
              onClick={() => {
                updateRouterQuery(item.reportId)
              }}>
              <TooltipProvider delayDuration={0} data-component='MenuItemTitle'>
                <Tooltip>
                  <TooltipTrigger
                    data-component='TooltipTrigger'
                    className='flex-1 overflow-hidden text-ellipsis whitespace-nowrap pr-6'
                    data-state='instant-open'>
                    <div className='flex items-center'>
                      <DocumentTextIcon className='mr-1.5 h-6 w-6'></DocumentTextIcon>
                      <Text
                        className='flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-left'
                        type={TextEnum.Body_medium}>
                        <span>{item.title}</span>
                      </Text>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    side='right'
                    sideOffset={16}
                    className='rounded-sm border-none bg-tooltip'>
                    <div className='max-h-[74px] max-w-80 overflow-y-hidden text-ellipsis p-2 text-left text-tooltip-foreground'>
                      <Text type={TextEnum.Body_small}>{item.title}</Text>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {deleteReportId === item.reportId ? (
                <TrashIcon
                  onClick={(e) => {
                    // 阻止默认事件，防止选中
                    e.stopPropagation()
                    onDeleteCatalog(item)
                  }}
                  className='absolute bottom-4 right-3.5 h-4 w-4'></TrashIcon>
              ) : null}
            </li>
          ))}
          {createLoading ? <DocSkeleton className='h-10 w-72 rounded-md'></DocSkeleton> : null}
          <ModalDelete
            open={visibleDelete}
            onClose={() => setVisibleDelete(false)}
            onConfirm={onDeleteConfirm}></ModalDelete>
        </ul>
      </div>
    </div>
  )
}
