import { ChatBubbleBottomCenterTextIcon } from '@heroicons/react/24/outline'
import { Text, TextEnum } from '@/components/business/text'
import { TextHtml } from '../TextHtml'

interface ChatProps {
  text: string
  title: string
}
export const Chat: React.FC<ChatProps> = ({ item }) => {
  return (
    <div className='mt-2 flex overflow-hidden rounded-sm bg-primary-bg px-2 py-3'>
      <ChatBubbleBottomCenterTextIcon
        className='mr-2 shrink-0 translate-y-1'
        width={32}
        height={22}></ChatBubbleBottomCenterTextIcon>
      <div>
        <Text type={TextEnum.H5} className='line-clamp-1'>
          {item.title}
        </Text>
        <div className='max-h-[220px] overflow-hidden text-ellipsis break-words'>
          {item.messages
            .filter((m) => !Array.isArray(m.content))
            .map((message, i) => {
              return <TextHtml key={i} text={message.content}></TextHtml>
            })}
        </div>
      </div>
    </div>
  )
}
