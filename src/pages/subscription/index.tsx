import { getUser } from '@/api/getUser'
import {
  getSubscriptionDetail,
  GetSubscriptionDetailResponse,
} from '@/api/subscription/getSubscriptionDetail'
import { HelpCenter } from '@/components/business/help-center'
import { PageHeader } from '@/components/business/page-header'
import { Namespace } from '@/i18n'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import ChangePlanPage from './ChangePlanPage'
import SwitchButton, { SwitchType } from './components/SwitchButton'
import ReferralPlanPage from './ReferralPlanPage'
import SubscriptionPage from './SubscriptionPage'
const Subscription = () => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)

  const router = useRouter()
  const { tab } = router.query
  const [selectedType, setSelectedType] = useState<SwitchType>(
    (tab as SwitchType) ?? 'subscription',
  )

  const updateUser = useUserStore((state) => state.updateUser)
  const [subscriptionDetail, setSubscriptionDetail] = useState<GetSubscriptionDetailResponse>()

  useEffect(() => {
    // detail
    updateSubscriptionDetail()
    updateUserDetail()
  }, [selectedType])

  const updateSubscriptionDetail = () => {
    getSubscriptionDetail().then((data) => {
      setSubscriptionDetail(data)
    })
  }

  const updateUserDetail = () => {
    getUser().then((data) => {
      if (data.userId) {
        updateUser(data)
      }
    })
  }

  const switchPage = () => {
    setSelectedType(selectedType === 'subscription' ? 'changePlan' : 'subscription')
  }
  // useEffect(() => {
  //   Tracking.trackPageView({ eventName: 'VISIT_SUBSCRIPTION_PAGE' })
  // }, [])
  return (
    <>
      <div
        className='flex h-full min-h-screen w-full min-w-fit max-w-full flex-col bg-card'
        asm-tracking='TEST_VISIT_SUBSCRIPTION_PAGE:VIEW'>
        <PageHeader />
        <div className='hide-scrollbar ml-[230px] h-screen-minus-header min-w-fit flex-row overflow-y-scroll'>
          <div className={clsx('mt-[50px] text-[36px] font-bold leading-[38px]')}>{t('title')}</div>

          <SwitchButton
            type={selectedType}
            onClick={setSelectedType}
            changePlanVisible={
              !!(subscriptionDetail && subscriptionDetail.currentPlan !== SubscriptionEnum.FREE)
            }
          />

          {selectedType === 'subscription' ? (
            <SubscriptionPage subscriptionDetail={subscriptionDetail} onChangePage={switchPage} />
          ) : null}
          {selectedType === 'changePlan' ? (
            <ChangePlanPage subscriptionDetail={subscriptionDetail} onChangePage={switchPage} />
          ) : null}
          {selectedType === 'referral' ? <ReferralPlanPage /> : null}
        </div>
      </div>
      <HelpCenter />
    </>
  )
}

export default Subscription
