import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
// import { formatTime } from '../utils/formatTime'
// import { useUserStore } from '@/store/userStore'

export interface CancelPlanModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  curPeriodEndsAt?: number | null
}
const CancelPlanModal: React.FC<CancelPlanModalProps> = ({
  open,
  onClose,
  onConfirm,
  curPeriodEndsAt,
}: CancelPlanModalProps) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.SUBSCRIPTION])

  // const user = useUserStore((state) => state.user)

  const handleCancel = () => {
    if (onClose) onClose()
  }
  const handleConfirm = () => {
    if (onConfirm) onConfirm()
  }

  return (
    <>
      <Modal
        open={open}
        showCloseIcon
        className='w-[434px] break-all p-6'
        onClose={handleCancel}
        overlayClose={false}>
        <>
          <Text type={TextEnum.H4}>{t('subscription:cancelModal.title')}</Text>

          <Text type={TextEnum.Body_big} className='my-5 break-keep'>
            {curPeriodEndsAt &&
              t('subscription:cancelModal.content', {
                date: new Date(curPeriodEndsAt)
                  .toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                  })
                  .replace(/\//g, '-'),
              })}
          </Text>

          <div className='text-right'>
            <Button onClick={handleCancel} variant='outline'>
              {t('button.cancel')}
            </Button>
            <Button onClick={handleConfirm} className='ml-2'>
              {t('button.confirm')}
            </Button>
          </div>
        </>
      </Modal>
    </>
  )
}

export default CancelPlanModal
