import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'

export interface UnableChangeModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
}
const UnableChangeModal: React.FC<UnableChangeModalProps> = ({
  open,
  onClose,
  onConfirm,
}: UnableChangeModalProps) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.SUBSCRIPTION])

  const handleCancel = () => {
    if (onClose) onClose()
  }
  const handleConfirm = () => {
    if (onConfirm) onConfirm()
  }

  return (
    <>
      <Modal
        open={open}
        showCloseIcon
        className='w-[434px] break-all p-6'
        onClose={handleCancel}
        overlayClose={false}>
        <>
          <Text type={TextEnum.H4}>{t('subscription:unableChange.title')}</Text>

          <Text type={TextEnum.Body_big} className='my-5 break-keep'>
            {t('subscription:unableChange.content')}
          </Text>

          <div className='text-right'>
            <Button onClick={handleCancel} variant='outline'>
              {t('button.cancel')}
            </Button>
            <Button onClick={handleConfirm} className='ml-2'>
              {t('button.confirm')}
            </Button>
          </div>
        </>
      </Modal>
    </>
  )
}

export default UnableChangeModal
