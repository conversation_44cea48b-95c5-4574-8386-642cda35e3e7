import { GetSubscriptionDetailResponse } from '@/api/subscription/getSubscriptionDetail'
import { SubscriptionEnum } from '@/types'
import FreePlanDetail from './FreePlanDetail'
import SubDetailCards from './SubDetailCards'

const SubDetailInfo = ({
  subscriptionDetail,
  onChangePage,
}: {
  subscriptionDetail: GetSubscriptionDetailResponse | undefined
  onChangePage: () => void
}) => {
  return (
    <>
      {subscriptionDetail?.currentPlan === SubscriptionEnum.FREE ? (
        <FreePlanDetail />
      ) : (
        <SubDetailCards subscriptionDetail={subscriptionDetail} onChangePage={onChangePage} />
      )}
    </>
  )
}

export default SubDetailInfo
