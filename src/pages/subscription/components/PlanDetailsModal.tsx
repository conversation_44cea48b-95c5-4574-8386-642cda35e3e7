import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { ContentItem, ContentList } from '@/pages/pricing/PricingCard'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'
import { useTranslation } from 'react-i18next'

export interface ContentType {
  plan: SubscriptionEnum
  title: string
  price: string
  priceText: string
  contents: {
    credits?: ContentItem[]
    exploration?: ContentItem[]
    file?: ContentItem[]
    exclusiveFeat?: ContentItem[]
    bespoke?: ContentItem[]
  }
}

export interface CreditsModalProps {
  open: boolean
  onClose: () => void
  onSelect: ({ newPlan }: { newPlan: SubscriptionEnum }) => void
  content?: ContentType
}
const PlanDetailsModal: React.FC<CreditsModalProps> = ({
  open,
  content,
  onClose,
  onSelect,
}: CreditsModalProps) => {
  const { t } = useTranslation([Namespace.SUBSCRIPTION, Namespace.PRICING])
  const user = useUserStore((state) => state.user)

  const handleCloseModal = () => {
    if (onClose) onClose()
  }
  const handleConfirm = () => {
    if (onSelect && content?.plan)
      onSelect({
        newPlan: content?.plan,
      })
  }

  return (
    <>
      <Modal
        open={open}
        showCloseIcon
        className='w-[434px] scroll-auto break-all p-6'
        onClose={handleCloseModal}>
        <>
          <Text type={TextEnum.H4} className='mt-1'>
            {content?.title}
          </Text>

          <div className='custom-scrollbar max-h-[60vh] overflow-y-scroll'>
            <div className='mb-10 mt-5 flex flex-row items-end'>
              <Text type={TextEnum.Title_big} className='text-primary'>
                {content?.price}
              </Text>
              <Text type={TextEnum.Body_medium} className='mb-[6px] text-secondary-black-2'>
                {content?.priceText}
              </Text>
            </div>

            {content?.contents.credits ? (
              <ContentList
                title={t('pricing:card.content.credits.title')}
                list={content.contents.credits ?? []}
              />
            ) : null}
            {content?.contents.exploration ? (
              <ContentList
                title={t('pricing:card.content.exploration.title')}
                list={content?.contents.exploration ?? []}
              />
            ) : null}

            {content?.contents.file ? (
              <ContentList
                title={t('pricing:card.content.fileStorage.title')}
                list={content.contents.file ?? []}
              />
            ) : null}

            {content?.contents.exclusiveFeat ? (
              <ContentList
                title={t('pricing:card.content.exclusiveFeature.title')}
                list={content.contents.exclusiveFeat ?? []}
              />
            ) : null}
            {content?.contents.bespoke ? (
              <ContentList
                title={t('pricing:card.content.bespoke.title')}
                list={content.contents.bespoke ?? []}
              />
            ) : null}
          </div>
          {user?.currentPlan === content?.plan ? null : (
            <div className='mt-3 text-right'>
              <Button onClick={handleConfirm}>{t('cardInfo.btn.select')}</Button>
            </div>
          )}
        </>
      </Modal>
    </>
  )
}

export default PlanDetailsModal
