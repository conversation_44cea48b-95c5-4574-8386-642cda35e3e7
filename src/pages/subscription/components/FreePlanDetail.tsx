import { TextEnum, Text } from '@/components/business/text'
import InfoCard from './InfoCard'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'

const FreePlanDetail = () => {
  const router = useRouter()
  const { t } = useTranslation(Namespace.SUBSCRIPTION)

  const handleUpgrade = () => {
    router.push(smartiesRoutes.pricing)
  }
  return (
    <InfoCard
      classes='rounded-2xl'
      header={{
        title: t('freeCard.title'),
        tagText: t('tag.active'),
        active: true,
      }}>
      <>
        <div className='mt-[22px] flex items-end gap-1'>
          <Text type={TextEnum.Title_big}>{t('freeCard.number')}</Text>
          <Text type={TextEnum.H6} className='mb-1'>
            {t('common.monthText')}
          </Text>
        </div>
        <Text type={TextEnum.H6} className='cursor-pointer text-primary' onClick={handleUpgrade}>
          {t('cardInfo.btn.upgrade')}
        </Text>
      </>
    </InfoCard>
  )
}

export default FreePlanDetail
