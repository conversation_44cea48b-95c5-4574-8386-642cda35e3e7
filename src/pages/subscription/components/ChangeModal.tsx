import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
// import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
// import { Label } from '@/components/ui/label'
// import { useState } from 'react'

export enum RadioValues {
  Immdediately = 'Immdediately',
  NextMonth = 'NextMonth',
}

export interface ChangeModalProps {
  disabled: boolean
  open: boolean
  onClose: () => void
  onConfirm: () => void
}

const ChangeModal: React.FC<ChangeModalProps> = ({
  disabled,
  open,
  onClose,
  onConfirm,
}: ChangeModalProps) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.SUBSCRIPTION])
  // const [value, setValue] = useState<RadioValues>(RadioValues.Immdediately)

  const handleCancel = () => {
    if (onClose) onClose()
  }
  const handleConfirm = () => {
    if (onConfirm) onConfirm()
  }

  // const handleChange = (value: string) => {
  //   setValue(value as RadioValues)
  // }

  return (
    <>
      <Modal
        showCloseIcon
        open={open}
        className='w-[434px] break-all p-6'
        onClose={handleCancel}
        overlayClose={false}>
        <>
          <Text type={TextEnum.H4} className='mb-5'>
            {t('subscription:changeModal.title')}
          </Text>

          <Text type={TextEnum.Body_big} className='mb-5'>
            {t('subscription:changeModal.content')}
          </Text>
          {/* <Text type={TextEnum.H4}>{t('subscription:changeModal.title')}</Text>
          <div>
            <RadioGroup
              defaultValue={RadioValues.Immdediately}
              className='my-5'
              onValueChange={handleChange}>
              <div className='flex items-center gap-2'>
                <RadioGroupItem value={RadioValues.Immdediately} id='r1' />
                <Label htmlFor='r1'>{t('subscription:changeModal.immediately')}</Label>
              </div>
              <div className='flex items-center gap-2'>
                <RadioGroupItem value={RadioValues.NextMonth} id='r2' />
                <Label htmlFor='r2'>{t('subscription:changeModal.nextMonth')}</Label>
              </div>
            </RadioGroup>
          </div> */}

          <div className='text-right'>
            <Button onClick={handleCancel} variant='outline'>
              {t('button.cancel')}
            </Button>
            <Button onClick={handleConfirm} className='ml-2' disabled={disabled}>
              {t('button.confirm')}
            </Button>
          </div>
        </>
      </Modal>
    </>
  )
}

export default ChangeModal
