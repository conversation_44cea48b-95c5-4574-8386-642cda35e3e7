import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import Image from 'next/image'
import CopyToClipboard from 'react-copy-to-clipboard'
import { toast } from 'sonner'
import { DocumentDuplicateIcon } from '@heroicons/react/24/outline'
import { Namespace } from '@/i18n'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { TextEnum, Text } from '@/components/business/text'
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table'
import { getReferralInfo, GetReferralInfoResponse } from '@/api/subscription/getReferralInfo'
import { Button } from '@/components/ui/button'
import { useUserStore } from '@/store/userStore'
import { smartiesRoutes } from '@/routers'

const points = '/images/points.svg'
const friends = '/images/friends.svg'

const ReferralPlanPage = () => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)
  const user = useUserStore((state) => state.user)
  const [referralInfo, setReferralInfo] = useState<GetReferralInfoResponse>()
  const [referralUrl, setReferralUrl] = useState<string>()
  const [initd, setInitd] = useState(false)

  useEffect(() => {
    getReferralInfo()
      .then((data) => {
        setReferralInfo(data)
        setReferralUrl(
          `${window.location.origin}${smartiesRoutes.signup}?referralCode=${data.referralCode}`,
        )
      })
      .catch((e) => console.log(e))
      .finally(() => setInitd(true))
  }, [])

  if (!initd) return null

  return (
    <div className='w-fit pb-6' data-component='ReferralPlanPage'>
      <Text type={TextEnum.H4} className={'mb-4 mt-11'}>
        {t('referral.title')}
      </Text>
      <div className='flex'>
        <div className='box-border w-[456px] rounded-lg border border-secondary-hover px-7 py-6'>
          <Text type={TextEnum.H2}>{t('referral.card1.title')}</Text>
          <Text type={TextEnum.H4} className={'mb-2.5 mt-8'}>
            {t('referral.card1.subTitle')}
          </Text>
          <Text type={TextEnum.Body_big} className={'text-secondary-black-3'}>
            {t('referral.card1.tips1')}
          </Text>
          <Text type={TextEnum.Body_big} className={'text-secondary-black-3'}>
            {t('referral.card1.tips2')}
          </Text>
          <Text type={TextEnum.Body_big} className={'text-secondary-black-3'}>
            {t('referral.card1.tips3')}
          </Text>
        </div>
        <div className='ml-4 flex w-[456px] flex-col justify-between'>
          <div className='box-border w-full rounded-lg border border-secondary-hover px-7 py-6'>
            <Text type={TextEnum.H3}>{t('referral.card2.title')}</Text>
            <Text type={TextEnum.Body_big} className={'box-border py-2.5'}>
              {`${user?.name ?? ''} `}
              {t('referral.card2.subTitle')}
            </Text>
            <div className='flex w-full'>
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger>
                    <div className='box-border w-[290px] min-w-0 flex-auto rounded-md border border-secondary-hover p-3'>
                      <Text type={TextEnum.Body_medium} className={'w-full truncate'}>
                        {referralUrl}
                      </Text>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <Text type={TextEnum.Body_medium}>{referralUrl}</Text>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <CopyToClipboard text={referralUrl || ''} onCopy={() => toast(t('referral.copy'))}>
                <Button className='ml-2.5 flex-none'>
                  <DocumentDuplicateIcon width={16} className='mr-0.5' />
                  {t('referral.card2.button')}
                </Button>
              </CopyToClipboard>
            </div>
          </div>
          <div className='mt-4 flex w-full justify-between'>
            <div className='box-border flex w-[220px] flex-col items-center justify-center rounded-lg border border-secondary-hover px-6 py-4'>
              <div className='h-8 w-8 rounded-full bg-[#fff7dc]'>
                <Image
                  src={points}
                  width={32}
                  height={32}
                  alt='points'
                  className={'h-full w-full'}
                />
              </div>
              <Text type={TextEnum.Body_big} className={'my-2.5 text-secondary-black-3'}>
                {t('referral.card3.title')}
              </Text>
              <Text type={TextEnum.H3}>{referralInfo?.creditEarned || 0}</Text>
            </div>
            <div className='box-border flex w-[220px] flex-col items-center justify-center rounded-lg border border-secondary-hover px-6 py-4'>
              <div className='h-8 w-8 rounded-full bg-[#b6bed380]'>
                <Image
                  src={friends}
                  width={32}
                  height={32}
                  alt='points'
                  className={'h-full w-full'}
                />
              </div>
              <Text type={TextEnum.Body_big} className={'my-2.5 text-secondary-black-3'}>
                {t('referral.card4.title')}
              </Text>
              <Text type={TextEnum.H3}>{referralInfo?.referralsCount || 0}</Text>
            </div>
          </div>
        </div>
      </div>
      {/* <Text type={TextEnum.H4} className={'mb-4 mt-11'}>
        {t('referral.table.title')}
      </Text>
      <Table className='min-w-[1014px] border-b'>
        <TableHeader className='text-foreground'>
          <TableRow className='bg-gray-tabHeader text-base text-secondary-black-3'>
            <TableHead>{t('referral.table.header.date')}</TableHead>
            <TableHead>{t('referral.table.header.plan')}</TableHead>
            <TableHead>{t('referral.table.header.status')}</TableHead>
            <TableHead>{t('referral.table.header.earned')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody></TableBody>
      </Table> */}
    </div>
  )
}

export default ReferralPlanPage
