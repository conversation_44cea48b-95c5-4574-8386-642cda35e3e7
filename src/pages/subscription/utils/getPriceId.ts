import { SubscriptionEnum } from '@/types'

export const subscriptionPriceId = {
  BASIC_MONTH: process.env.NEXT_PUBLIC_PADDLE_BASIC_MONTH_PRICE_ID!,
  BASIC_YEAR: process.env.NEXT_PUBLIC_PADDLE_BASIC_YEAR_PRICE_ID!,
  PRO_MONTH: process.env.NEXT_PUBLIC_PADDLE_PRO_MONTH_PRICE_ID!,
  PRO_YEAR: process.env.NEXT_PUBLIC_PADDLE_PRO_YEAR_PRICE_ID!,
}

export const getPriceId = (plan?: SubscriptionEnum) => {
  if (!plan) return undefined
  return subscriptionPriceId[plan as keyof typeof subscriptionPriceId]
}
