import { SupportedLangs } from '@/i18n'

// return Oct 16, 2024
export const formatTime = (time: number, lang: SupportedLangs) => {
  const date = new Date(time)

  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }

  const formattedDate = date.toLocaleDateString(
    lang === SupportedLangs.ZH ? 'zh-CN' : 'en-US',
    options,
  )
  return formattedDate
}
