import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { TextEnum, Text } from '@/components/business/text'
import { TransTable } from './components/TransTable'
import { listTransaction, ListTransactionResponse } from '@/api/subscription/listTransaction'
import { useEffect, useState } from 'react'
import { GetSubscriptionDetailResponse } from '@/api/subscription/getSubscriptionDetail'
import BankDetail from './components/BankDetail'
import SubDetailInfo from './components/SubDetailInfo'

const SubscriptionPage = ({
  subscriptionDetail,
  onChangePage,
}: {
  subscriptionDetail?: GetSubscriptionDetailResponse
  onChangePage: () => void
}) => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)
  const [list, setList] = useState<ListTransactionResponse>([])

  useEffect(() => {
    // list
    listTransaction().then((data) => {
      setList(data)
    })
  }, [])

  return (
    <div className='w-fit pb-6'>
      <Text type={TextEnum.H4} className={'mb-4 mt-11'}>
        {t('currentPlan.title')}
      </Text>

      <SubDetailInfo subscriptionDetail={subscriptionDetail} onChangePage={onChangePage} />

      {subscriptionDetail?.paymentMethod && subscriptionDetail.paymentMethod.card && (
        <>
          <Text type={TextEnum.H4} className={'mb-4 mt-7'}>
            {t('paymentInfo.title')}
          </Text>
          <BankDetail subscriptionDetail={subscriptionDetail} />
        </>
      )}

      {list && list.length > 0 && (
        <>
          <Text type={TextEnum.H4} className={'mb-4 mt-7'}>
            {t('transactionInfo.title')}
          </Text>
          <div className='w-full'>
            <TransTable transactions={list} />
          </div>
        </>
      )}
    </div>
  )
}

export default SubscriptionPage
