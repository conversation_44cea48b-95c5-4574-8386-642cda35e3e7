import { GetServerSidePropsContext, NextPageContext } from 'next'
import { setCookie, smartiesDomain } from './cookie'

export const clearCookies = (ctx?: NextPageContext | GetServerSidePropsContext) => {
  // 清理带 domain 的 cookie
  setCookie(
    'smartToken',
    '',
    {
      maxAge: 0,
      httpOnly: false,
      domain: smartiesDomain,
    },
    ctx,
  )

  // 清理不带 domain 的 cookie（兼容旧版本）
  setCookie(
    'smartToken',
    '',
    {
      maxAge: 0,
      httpOnly: false,
    },
    ctx,
  )
}
