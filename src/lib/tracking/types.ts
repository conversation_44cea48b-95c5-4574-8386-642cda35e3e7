/**
 * 定义事件类型枚举
 */
export enum TrackingEventType {
  VIEW = 'VIEW',
  CLICK = 'CLICK',
  HOVER = 'HOVER',
  SUBMIT = 'SUBMIT',
  CHANGE = 'CHANGE',
  FOCUS = 'FOCUS',
  BLUR = 'BLUR',
  KEYPRESS = 'KEYPRESS',
}

/**
 * 定义追踪属性接口
 */
export interface TrackingProperties {
  [key: string]: unknown
}

/**
 * 定义追踪事件接口
 */
export interface TrackingEvent {
  name: string
  type: TrackingEventType
  properties?: TrackingProperties
}

/**
 * 定义追踪配置接口
 */
export interface TrackingConfig {
  debug?: boolean
  disableAutoTracking?: boolean
  customEventPrefix?: string
}

/**
 * 定义元素追踪信息接口
 */
export interface ElementTrackingInfo {
  eventName: string
  eventType: TrackingEventType
  customMethod?: string
  params: TrackingProperties
}

/**
 * 定义自定义追踪方法类型
 */
export type CustomTrackingMethod = (defaultParams: TrackingProperties) => TrackingProperties
