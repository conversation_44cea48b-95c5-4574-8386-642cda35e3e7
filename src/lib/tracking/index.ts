import { TrackingEventType } from './types'
import { getCommonProperties } from './common'
import { autoTracking } from './auto-tracking'
import { trackingHighlight } from './tracking-highlight'
import { debugTracking } from './debug'
import mixpanel from 'mixpanel-browser'

// 初始化 Mixpanel
// 注意：在实际使用时，你需要将 token 放在环境变量中
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || ''

// 只在客户端初始化 Mixpanel
if (typeof window !== 'undefined') {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV !== 'production',
    track_pageview: false, // 我们会手动追踪页面浏览
    ignore_dnt: true, // 忽略 "Do Not Track"（DNT）设置
  })
}

/**
 * 主要的追踪功能接口
 */
export const Tracking = {
  /**
   * 手动追踪页面浏览事件
   */
  async trackPageView(pageName: string, properties = {}) {
    if (typeof window === 'undefined') return

    try {
      const commonProps = await getCommonProperties()

      // 合并通用属性和自定义属性
      const allProperties = {
        ...commonProps,
        page_name: pageName,
        ...properties,
      }

      // 发送到 Mixpanel
      mixpanel.track('Page View', allProperties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] Page View:', pageName, allProperties)
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to track page view:', error)
    }
  },

  /**
   * 手动追踪自定义事件
   */
  async trackEvent(eventName: string, eventType: TrackingEventType, properties = {}, test = true) {
    if (
      typeof window === 'undefined' ||
      !process.env.NEXT_PUBLIC_ENV ||
      process.env.NEXT_PUBLIC_ENV === 'development'
    )
      return

    try {
      const commonProps = await getCommonProperties()

      // 合并通用属性和自定义属性
      const allProperties = {
        ...commonProps,
        event_type: eventType,
        ...properties,
      }

      eventName = test ? `TEST_${eventName}` : eventName

      // 发送到 Mixpanel
      mixpanel.track(eventName, allProperties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Tracking] ${eventType} Event:`, eventName, allProperties)
      }
    } catch (error) {
      console.error(`[Tracking Error] Failed to track ${eventType} event:`, error)
    }
  },

  /**
   * 初始化自动追踪功能
   * 应在应用初始化时调用
   */
  initAutoTracking() {
    if (typeof window === 'undefined') return

    try {
      // 初始化自动追踪
      autoTracking.init()

      // 显式初始化追踪高亮功能
      trackingHighlight.init()

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] Auto-tracking and highlight feature initialized')
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to initialize tracking:', error)
    }
  },

  /**
   * 识别用户
   */
  identifyUser(userId: string, userProperties = {}) {
    if (typeof window === 'undefined') return

    try {
      // 在 Mixpanel 中识别用户
      mixpanel.identify(userId)

      // 设置用户属性
      if (Object.keys(userProperties).length > 0) {
        mixpanel.people.set(userProperties)
      }

      // 存储用户ID
      localStorage.setItem('userId', userId)

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] User identified:', userId, userProperties)
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to identify user:', error)
    }
  },

  /**
   * 重置用户（退出登录时调用）
   */
  resetUser() {
    if (typeof window === 'undefined') return

    try {
      // 重置 Mixpanel 用户
      mixpanel.reset()

      // 移除存储的用户ID
      localStorage.removeItem('userId')

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] User reset')
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to reset user:', error)
    }
  },

  /**
   * 初始化追踪高亮功能
   * 可以在任何页面上单独调用此方法来启用高亮功能
   */
  initTrackingHighlight() {
    if (typeof window === 'undefined') return

    try {
      trackingHighlight.init()

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] Highlight feature initialized')
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to initialize highlight feature:', error)
    }
  },
}

// 导出类型
export { TrackingEventType } from './types'

// 导出追踪高亮功能
export { trackingHighlight }

// 导出调试工具
export { debugTracking }
