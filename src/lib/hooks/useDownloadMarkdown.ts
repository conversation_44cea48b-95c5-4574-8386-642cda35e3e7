import { ReportItemType } from '@/api/document'
import { Namespace } from '@/i18n'
import {
  Document,
  ExternalHyperlink,
  HeadingLevel,
  ImageRun,
  Packer,
  Paragraph,
  Table,
  TableCell,
  TableRow,
  TextRun,
  LevelFormat,
  AlignmentType,
} from 'docx'
import { saveAs } from 'file-saver'
import { toJpeg } from 'html-to-image'
import { Marked, Tokens } from 'marked'
import mermaid from 'mermaid'
import { useCallback, useRef } from 'react'
import { useTranslation } from 'react-i18next'

interface RefItem {
  type: 'PDF'
  ref: {
    snippet: string
    s3Url: string
    title: string
    url: string
  }
}
function processSrefs(html: string): {
  updatedHtml: string
  referencesList: { title: string; url: string }[]
} {
  const srefRegex = /<sref\s+[^>]*?r_url="([^"]+)"\s+[^>]*?r_title="([^"]+)"[^>]*?>R<\/sref>/g

  const seenTitleToIndex = new Map<string, number>()
  const referencesList: { title: string; url: string }[] = []
  let index = 1

  const updatedHtml = html.replace(srefRegex, (_match, url, title) => {
    let refIndex
    if (seenTitleToIndex.has(title)) {
      refIndex = seenTitleToIndex.get(title)!
    } else {
      refIndex = index++
      seenTitleToIndex.set(title, refIndex)
      referencesList.push({ title, url })
    }
    return `[${refIndex}]`
  })

  return { updatedHtml, referencesList }
}

export const useDownloadMarkdown = (
  report: ReportItemType | { content: string; title?: string },
) => {
  const { t } = useTranslation([Namespace.BASIC_SEARCH])
  const markdownContent = report.content || ''
  const title = report.title || ''

  const numberingRefs = useRef(new Set<string>())

  const renderMermaidToSvg = async (code: string) => {
    try {
      mermaid.initialize({
        startOnLoad: true,
        theme: 'default',
      })
      const { svg } = await mermaid.render('mermaid-temp', code)
      const parser = new DOMParser()
      const doc = parser.parseFromString(svg, 'image/svg+xml')
      const svgElement = doc.querySelector('svg')
      if (svgElement) {
        svgElement.setAttribute('style', 'background-color: white;')
      }
      console.log(svgElement, 'svgElement')
      return svgElement
    } catch (err) {
      console.warn('Mermaid render error:', err)
      return code
    }
  }

  const customExtensions = [
    {
      name: 'customText',
      level: 'inline',
      start: (src: string) => src.match(/\[\^\d+\^\]/)?.index,
      tokenizer(src: string) {
        const match = /^\[\^(\d+)\^\]/.exec(src)
        return match ? { type: 'customText', raw: match[0], text: match[1] } : false
      },
      renderer: (token: Tokens.Generic) => `[${token.text}]`,
    },
    {
      name: 'pageNumberText',
      level: 'inline',
      start: (src: string) => src.match(/\[\^\^\d+\^\^\]/)?.index,
      tokenizer(src: string) {
        const match = /^\[\^\^(\d+)\^\^\]/.exec(src)
        return match ? { type: 'pageNumberText', raw: match[0], text: match[1] } : false
      },
      renderer: (token: Tokens.Generic) => `[${token.text}]`,
    },
    {
      name: 'customRefs',
      level: 'inline',
      start: (src: string) => src.indexOf(':::component'),
      tokenizer(src: string) {
        const match = /:::component([\s\S]*?):::/.exec(src)
        return match ? { type: 'customRefs', raw: match[0], text: match[1] } : false
      },
      renderer: () => '',
    },
  ]

  const marked = new Marked({
    extensions: customExtensions,
    gfm: true,
    breaks: true,
  })

  const handlePreTag = (element: HTMLElement) => {
    const preContent = element.textContent || ''

    // 查找预格式化文本内部是否有 <code> 标签
    const codeElement = element.querySelector('code')

    if (codeElement) {
      // 处理 <code> 标签中的内容
      const codeContent = codeElement.textContent || ''

      return new Paragraph({
        children: [
          new TextRun({
            text: codeContent,
            bold: false, // 保持代码块为正常文本
            color: '000000', // 设置颜色
            font: 'Courier New', // 设置等宽字体
          }),
        ],
      })
    } else {
      // 如果没有 <code> 标签，处理整个 <pre> 内容
      return new Paragraph({
        children: [
          new TextRun({
            text: preContent,
            bold: false, // 保持代码块为正常文本
            color: '000000', // 设置颜色
            font: 'Courier New', // 设置等宽字体
          }),
        ],
      })
    }
  }

  const handleListItems = (
    element: HTMLElement,
    isOrdered: boolean,
    level: number,
    paragraphs: Array<Paragraph | Table>,
    listId: number,
  ) => {
    const items = Array.from(element.children).filter((el) => el.tagName === 'LI')

    items.forEach((item) => {
      const reference = `list-${listId}-${isOrdered ? 'ol' : 'ul'}`
      numberingRefs.current.add(reference)

      // 提取纯文本（不包括嵌套列表）
      const nested = Array.from(item.children).find(
        (child) => child.tagName === 'OL' || child.tagName === 'UL',
      )
      const textParts: string[] = []
      for (const child of item.childNodes) {
        if (!(child instanceof HTMLElement && (child.tagName === 'OL' || child.tagName === 'UL'))) {
          textParts.push(child.textContent || '')
        }
      }

      paragraphs.push(
        new Paragraph({
          text: textParts.join('').trim(),
          numbering: { reference, level },
        }),
      )

      // 递归处理嵌套
      if (nested instanceof HTMLElement) {
        handleListItems(nested, nested.tagName === 'OL', level + 1, paragraphs, listId + 1)
      }
    })
  }

  const handleTable = (tableElement: HTMLElement) => {
    const rows = Array.from(tableElement.querySelectorAll('tr'))
    const tableRows = rows.map((row) => {
      const cells = Array.from(row.querySelectorAll('td, th'))
      const tableCells = cells.map((cell) => {
        return new TableCell({
          children: [new Paragraph({ text: cell.textContent || '' })],
        })
      })

      return new TableRow({ children: tableCells })
    })

    return new Table({ rows: tableRows })
  }

  const exportToWord = useCallback(
    async (filename = 'document.docx') => {
      if (!markdownContent) return

      numberingRefs.current.clear()
      const html = marked.parse(markdownContent)
      const { updatedHtml, referencesList } = processSrefs(html as string)
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = updatedHtml as string

      const paragraphs: Array<Paragraph | Table> = []

      // 文档标题
      paragraphs.push(
        new Paragraph({
          heading: HeadingLevel.HEADING_1,
          spacing: { after: 300 },
          children: [new TextRun({ text: title, bold: true, color: '000000' })],
        }),
      )

      const createHeading = (
        text: string,
        level: (typeof HeadingLevel)[keyof typeof HeadingLevel] = HeadingLevel.HEADING_1,
      ) => {
        let fontSize = 12 // 默认字号为 12pt
        if (level === HeadingLevel.HEADING_2) fontSize = 12 // H2 设置为 12pt
        if (level === HeadingLevel.HEADING_3) fontSize = 10
        if (level === HeadingLevel.HEADING_4) fontSize = 8 // H4 设置为 8pt
        if (level === HeadingLevel.HEADING_5) fontSize = 6 // H5 设置为 6pt
        if (level === HeadingLevel.HEADING_6) fontSize = 4 // H6 设置为 4pt

        paragraphs.push(
          new Paragraph({
            heading: level,
            spacing: { before: 200, after: 200 },
            children: [
              new TextRun({
                text,
                color: '000000',
                bold: true,
                size: fontSize * 2, // size是以 1/2磅为单位，所以12pt需要传递24 (12*2)，10pt传递20
              }),
            ],
          }),
        )
      }

      const seenRefUrls = new Set<string>()
      const references: { title: string; url: string }[] =
        referencesList.length > 0 ? referencesList : []

      let listId = 0

      for (const node of tempDiv.childNodes) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          const tag = element.tagName

          if (tag === 'H1') createHeading(element.innerText, HeadingLevel.HEADING_1)
          else if (tag === 'H2') createHeading(element.innerText, HeadingLevel.HEADING_2)
          else if (tag === 'H3') createHeading(element.innerText, HeadingLevel.HEADING_3)
          else if (tag === 'H4') createHeading(element.innerText, HeadingLevel.HEADING_4)
          else if (tag === 'H5') createHeading(element.innerText, HeadingLevel.HEADING_5)
          else if (tag === 'H6') createHeading(element.innerText, HeadingLevel.HEADING_6)
          else if (tag === 'P') {
            paragraphs.push(
              new Paragraph({
                spacing: { before: 100, after: 100 },
                children: [new TextRun({ text: element.innerText, color: '000000' })],
              }),
            )
          } else if (tag === 'A') {
            paragraphs.push(
              new Paragraph({
                children: [
                  new ExternalHyperlink({
                    children: [new TextRun({ text: element.innerText, style: 'Hyperlink' })],
                    link: element.getAttribute('href') || '',
                  }),
                ],
              }),
            )
          } else if (tag === 'OL' || tag === 'UL') {
            const isOrdered = tag === 'OL'
            handleListItems(element, isOrdered, 0, paragraphs, listId++)
          } else if (tag === 'TABLE') {
            paragraphs.push(handleTable(element))
          } else if (tag === 'PRE') {
            const codeBlock = element.querySelector('code')
            const className = codeBlock?.className || ''
            if (className.includes('language-mermaid')) {
              const mermaidCode = codeBlock?.textContent || ''

              // 渲染 Mermaid 图为 SVG 元素
              const renderedSvg = await renderMermaidToSvg(mermaidCode)
              if (renderedSvg !== null && typeof renderedSvg !== 'string') {
                document.body.appendChild(renderedSvg)

                // 获取 SVG 原始尺寸
                const svgBBox = renderedSvg.getBBox()
                const originalWidth = svgBBox.width
                const originalHeight = svgBBox.height

                // 设置最大宽度限制，比如 600
                const maxWidth = 600
                const scale = maxWidth / originalWidth
                const finalWidth = originalWidth * scale
                const finalHeight = originalHeight * scale

                // 渲染成图像
                const dataUrl = await toJpeg(renderedSvg as unknown as HTMLElement)
                document.body.removeChild(renderedSvg)

                const imageRes = await fetch(dataUrl)
                const imageBlob = await imageRes.blob()
                const arrayBuffer = await imageBlob.arrayBuffer()

                // 插入 Word 文档并保持宽高比例
                paragraphs.push(
                  new Paragraph({
                    children: [
                      new ImageRun({
                        data: new Uint8Array(arrayBuffer),
                        type: 'jpg',
                        transformation: {
                          width: finalWidth,
                          height: finalHeight,
                        },
                      }),
                    ],
                  }),
                )
              }
            } else {
              paragraphs.push(handlePreTag(element))
            }
          }
        }
      }

      // 参考资料
      const match = markdownContent.match(/:::component([\s\S]*?):::/)
      if (match) {
        try {
          const refs: RefItem[] = JSON.parse(match[1].trim())
          refs.forEach(({ ref: { title, url } }) => {
            if (!seenRefUrls.has(url)) {
              seenRefUrls.add(url)
              references.push({ title, url })
            }
          })
        } catch (err) {
          console.error(err)
        }
      }

      if (references.length > 0) {
        paragraphs.push(
          new Paragraph({
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 },
            children: [new TextRun({ text: t('chat.refer'), color: '000000', bold: true })],
          }),
        )
        references.forEach((ref, index) => {
          paragraphs.push(
            new Paragraph({
              spacing: { after: 100 },
              children: [
                new TextRun({ text: `${index + 1}. ${ref.title} ` }),
                new ExternalHyperlink({
                  children: [new TextRun({ text: ref.url, style: 'Hyperlink' })],
                  link: ref.url,
                }),
              ],
            }),
          )
        })
      }

      // 动态生成 numbering config
      const numberingConfig = Array.from(numberingRefs.current).map((ref) => {
        const isOl = ref.includes('-ol')
        return {
          reference: ref,
          levels: [0, 1, 2].map((lvl) => ({
            level: lvl,
            format: isOl ? LevelFormat.DECIMAL : LevelFormat.BULLET,
            text: isOl ? `%${lvl + 1}.` : ['•', '◦', '▪'][lvl] || '•',
            alignment: AlignmentType.LEFT,
            style: {
              paragraph: {
                indent: {
                  left: 300,
                  hanging: 250,
                },
              },
            },
          })),
        }
      })

      const doc = new Document({
        numbering: { config: numberingConfig },
        sections: [{ properties: {}, children: paragraphs }],
      })

      const blob = await Packer.toBlob(doc)
      saveAs(blob, filename)
    },
    [markdownContent],
  )

  return { exportToWord }
}
