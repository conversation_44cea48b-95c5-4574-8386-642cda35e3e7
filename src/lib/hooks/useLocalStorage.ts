import { useEffect, useState } from 'react'
import { useLocalStorageStore } from '@/store/useLocalStorageStore'
export type LocalStorageType = string | null

export function useLocalStorage(key: string) {
  const setValue = useLocalStorageStore((s) => s.setValue)
  const getValue = useLocalStorageStore((s) => s.getValue)
  const [state, setState] = useState<LocalStorageType>()
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
    setState(getValue(key))
  }, [])

  const setWithLocalStorage = (nextState: NonNullable<LocalStorageType>) => {
    // localStorage.setItem(key, nextState)
    setValue(key, nextState)
    setState(nextState)
  }

  const removeLocalStorage = () => localStorage.removeItem(key)

  return [state, setWithLocalStorage, removeLocalStorage, isLoaded] as const
}
