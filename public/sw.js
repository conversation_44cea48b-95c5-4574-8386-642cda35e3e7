const CACHE_NAME = 'sm-pdf-cache-v1'
const PDF_CACHE = 'sm-pdf-files'

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll([])
    }),
  )
  self.skipWaiting() // 强制立即激活新的 Service Worker
})

self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url)
  if ((url.pathname ?? '').endsWith('.pdf')) {
    const cacheKey = url.origin + url.pathname
    event.respondWith(
      caches.match(cacheKey).then((response) => {
        console.log('caches', caches)
        if (response) {
          return response
        }
        return fetch(event.request).then((fetchResponse) => {
          return caches.open(PDF_CACHE).then((cache) => {
            cache.put(cacheKey, fetchResponse.clone())
            return fetchResponse
          })
        })
      }),
    )
  }
})

self.addEventListener('activate', (event) => {
  // 让新的 Service Worker 控制所有客户端
  event.waitUntil(() => {
    self.clients.claim()
  })
})
