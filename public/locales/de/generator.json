{"createDoc": "Neues Dokument", "docSetting": "Einstellung", "emptyDocument": "<PERSON>res <PERSON>", "businessPlan": "Geschäftsplan", "companyAnalysis": "Unternehmensanalyse", "setting": "Einstellung", "saveSource": "Gespeicherte Ressourcen", "description": "Beschreibung", "docDesc": "Wir generieren den Bericht basierend auf den von Ihnen ausgewählten Informationen. Wenn keine Informationen ausgewählt sind, wird der Bericht direkt mit dem Modell generiert.", "autoGenerator": "Automatische Generierung", "documentGenerating": "<PERSON><PERSON><PERSON> wird generiert", "deductTitle": "Die Generierung eines Berichts wird 4 Credits abziehen", "deepResearchDeductTitle": "Die Generierung eines detaillierten Berichts wird 10 Credits abziehen", "deepResearchPromptContent": "Sind <PERSON> sicher, dass Si<PERSON> fortfahren möchten", "confirm": "Bestätigen", "cancel": "Abbrechen", "promptContent": "Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "dotShow": "<PERSON><PERSON> Nachricht nicht mehr anzeigen.", "showAgain": "Mir nicht mehr anzeigen", "cancelGenerating": "Generierung abbrechen", "descriptionPlaceholder": "Bitte geben Sie die Beschreibung ein", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "titleEmpty": "<PERSON> Titel darf nicht leer sein.", "settingTips": "Dies ist das Rahmenwerk für den Berichtsplan. Sie können ihn nach Ihren Bedürfnissen ändern und anpassen.", "llmTips": "einen Online-Reiseagenten basierend auf LLM in den USA aufbauen...", "saveToDoc": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "confirmationModal": {"title": "Lassen Sie KI Ihre Suchziele verstehen", "subtitle": "<PERSON>e können auch den folgenden Inhalt überarbeiten, um das Verständnis zu verbessern.", "cancel": "Abbrechen", "confirm": "Bestätigen (100s)", "processing": "Verarbeitung...", "placeholder": "Bestätigungstext bearbeiten..."}, "deepResearch": {"outlineTitle": "Berichtsplan", "outlineDescription": "Überprüfen und passen Sie den Berichtsplan an, bevor <PERSON> mit der Recherche beginnen", "originalQuery": "Ursprüngliche Anfrage", "confirmationText": "Bestätigungstext", "processingOutline": "Plan wird verarbeitet...", "backToRequirement": "Zur<PERSON> zu den Anforderungen", "startResearchWithOutline": "Recherche mit Plan starten"}}