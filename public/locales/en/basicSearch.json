{"common": {"stop": "Stop Generating", "send": "Send"}, "homePage": {"card": {"title_market": "Market Research", "content_market": "AI analysts at your service", "title_topic": "Topic Research", "content_topic": "Auto search multiple sources", "title_swot": "SWOT Analysis", "content_swot": "Know your strengthen, weakness etc.", "title_company": "Company Research", "content_company": "Business canvas generation in minutes", "title_regulation": "Export Compliance", "content_regulation": "What you need to know for exporting products", "title_risk": "Potential Risks", "content_risk": "Identify risks and test methods to secure business"}, "auto": "Auto", "reasoning": "Reasoning", "sendNotification": "Notify me", "deepThinkFinish": "Thoughts completed", "deepSearchCompleted": "The report is generated.", "checkDeepSearchResult": "Click to View", "deepSearchProgressDesc": "Search, analyze, and summary the question progress is: ", "deepSearch": "Deep search", "deepSearchAgent": "Deep Search Agent", "deepSearchQuestionDescription": "We will expand the questions based on your inputs to help you better retrieve and summarize the information", "tip": "AI can make mistakes. Check important info.", "agent": "Smart Search", "agentDes": "Smart Search helps you find the best tool for your needs. If you turn off this function, it won't switch your search tool anymore.", "agentTitle": "Choose Tools to get better and structured results", "toPcNotice": "Thank you for registering, please get a better amount experience on your computer."}, "general": {"query": "Question", "placeholder": "Ask anything or paste a URL"}, "market": {"query1": "Business or Product Description", "query1Placeholder": "Type something like: 'sell pets GPS trackers'.", "tips1": "We recommend asking clear, targeted questions, focusing on one point at a time.", "query2": "Your Target Market", "query2Placeholder": "Type something like: 'Global', or 'Europe'.", "tips2": "You can focus on a specific region or start with a global overview."}, "topic": {"query1": "Topic", "query1Placeholder": "Type the main topic you want to research", "tips1": "We recommend asking clear, targeted questions, focusing on one point at a time.", "query2": "Details", "query2Placeholder": "Give key points or other information", "tips2": "Add details about the content you want to research."}, "swot": {"query1": "Business or Product Description", "query1Placeholder": "Tye something like: our pet GPS trackers are high quality and low cost, accurately monitoring pets' locations to keep them safe. ", "tips1": "Simply explain your product or services and your advantages. Don't be afraid that your description is not enough, we will guide you."}, "company": {"query1": "Company Name", "query1Placeholder": "Type the company name here, for example 'OpenAI'.", "tips1": "If there are multiple companies with the same name, add a detail, like \"Dify AI\""}, "oversea": {"query1": "Business, Product, or Services Description", "query1Placeholder": "Tye something like: 'laundry sanitizer'.", "tips1": "Get a full overview of export requirements, covering export/import countries and recent policy updates.", "query2": "Your Target Market", "query2Placeholder": "Tye the region or country you want to enter in, for example 'USA'.", "query3": "Your Current Location", "query3Placeholder": "Tye your current location, for example 'Spain'."}, "risk": {"query1": "Business or Product Description", "query1Placeholder": "Tye something like: 'export pets GPS trackers from Singapore to USA'.", "tips1": "Risk assessment identifies assumptions, detects risks, develops testing methods, formulates countermeasures, and enhances planning by preparing for challenges."}, "chat": {"statustext": {"search": "Searching", "extract": "Extracting", "summarize": "Summarizing"}, "input": {"placehoder": "Follow-Up"}, "rightSide": {"recommend": "Deep Explore", "recommendTip": "Click to view details", "noData": "No Data", "PDF": "PDF", "save": "Save to Deep File", "viewMore": "View More", "open": "Open", "saveToDoc": "Save"}, "refer": "Reference"}, "stopModal": {"content": "Are you sure to stop generating the content?"}, "imgChat": {"describeImg": "Describe this image"}}