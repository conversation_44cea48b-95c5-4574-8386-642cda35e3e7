{"createDoc": "Nouveau Document", "docSetting": "Paramètre", "emptyDocument": "Document Vide", "businessPlan": "Plan d'Affaires", "companyAnalysis": "<PERSON><PERSON><PERSON> d'Entreprise", "setting": "Paramètre", "saveSource": "Ressources Sauvegardées", "description": "Description", "docDesc": "Nous générerons le rapport en fonction des informations que vous sélectionnez. Si aucune information n'est sélectionnée, le rapport sera généré directement à l'aide du modèle.", "autoGenerator": "Génération Automatique", "documentGenerating": "Génération de document", "deductTitle": "La génération d'un rapport déduira 4 crédits", "deepResearchDeductTitle": "La génération d'un rapport approfondi déduira 10 crédits", "deepResearchPromptContent": "Êtes-vous sûr de vouloir continuer", "confirm": "Confirmer", "cancel": "Annuler", "promptContent": "Êtes-vous sûr de vouloir continuer ?", "dotShow": "Ne plus afficher ce message.", "showAgain": "Ne plus m'afficher", "cancelGenerating": "Annuler la Génération", "descriptionPlaceholder": "Veuillez entrer la description", "download": "Télécharger", "titleEmpty": "Le titre ne peut pas être vide.", "settingTips": "Ceci est le cadre du plan du rapport. Vous pouvez le modifier et le personnaliser selon vos besoins.", "llmTips": "construire un agent de voyage en ligne basé sur LLM aux États-Unis...", "saveToDoc": "Sauvegarder dans le rapport", "confirmationModal": {"title": "Laissez l'IA comprendre vos objectifs de recherche", "subtitle": "Vous pouvez également réviser le contenu suivant pour améliorer la compréhension.", "cancel": "Annuler", "confirm": "Confirmer (100s)", "processing": "Traitement...", "placeholder": "Modifier le texte de confirmation..."}, "deepResearch": {"outlineTitle": "Plan du Rapport", "outlineDescription": "Examinez et personnalisez le plan du rapport avant de commencer la recherche", "originalQuery": "Req<PERSON><PERSON>te <PERSON>e", "confirmationText": "Texte de Confirmation", "processingOutline": "Traitement du plan...", "backToRequirement": "Retour aux Exigences", "startResearchWithOutline": "Commencer la Recherche avec le Plan"}}